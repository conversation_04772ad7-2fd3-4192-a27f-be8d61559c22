/**
 * 设备核心参数监控系统 - 主要JavaScript文件
 */

class EquipmentMonitor {
    constructor() {
        this.chart = null;
        this.autoRefreshTimer = null;
        this.currentTab = 'monitoring';
        this.isLoading = false;
        
        this.init();
    }
    
    /**
     * 初始化应用
     */
    init() {
        this.bindEvents();
        this.initTabs();
        this.loadInitialData();
        this.startAutoRefresh();
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 筛选器变化
        document.getElementById('deviceSelect').addEventListener('change', () => this.updateModelOptions());
        document.getElementById('modelSelect').addEventListener('change', () => this.updateParamOptions());
        document.getElementById('paramSelect').addEventListener('change', () => this.loadChartData());
        document.getElementById('timeRange').addEventListener('change', () => this.loadChartData());
        
        // 按钮事件
        document.getElementById('refreshBtn').addEventListener('click', () => this.loadChartData());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());
        document.getElementById('refreshSpecBtn').addEventListener('click', () => this.loadSpecList());
        
        // 自动刷新开关
        document.getElementById('autoRefresh').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });
    }
    
    /**
     * 初始化标签页
     */
    initTabs() {
        this.switchTab('monitoring');
    }
    
    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
        
        this.currentTab = tabName;
        
        // 加载对应数据
        if (tabName === 'monitoring') {
            this.loadChartData();
        } else if (tabName === 'management') {
            this.loadSpecList();
        }
    }
    
    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            await this.loadFilterOptions();
            await this.loadStatistics();
        } catch (error) {
            this.showMessage('加载初始数据失败: ' + error.message, 'error');
        }
    }
    
    /**
     * 加载筛选选项
     */
    async loadFilterOptions() {
        try {
            const response = await fetch('php/get_options.php');
            const result = await response.json();
            
            if (result.success) {
                this.populateSelect('deviceSelect', result.data.devices);
                this.populateSelect('modelSelect', result.data.models);
                this.populateSelect('paramSelect', result.data.params);
                
                // 更新统计信息
                if (result.data.statistics) {
                    this.updateStatistics(result.data.statistics);
                }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载筛选选项失败:', error);
            this.showMessage('加载筛选选项失败', 'error');
        }
    }
    
    /**
     * 填充下拉选择框
     */
    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        const firstOption = select.querySelector('option');
        
        // 清空现有选项，保留第一个默认选项
        select.innerHTML = '';
        select.appendChild(firstOption);
        
        // 添加新选项
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            select.appendChild(optionElement);
        });
    }
    
    /**
     * 更新数据分类选项
     */
    async updateModelOptions() {
        const deviceMark = document.getElementById('deviceSelect').value;
        if (!deviceMark) return;
        
        try {
            const response = await fetch(`php/get_options.php?device_mark=${encodeURIComponent(deviceMark)}`);
            const result = await response.json();
            
            if (result.success && result.data.models) {
                this.populateSelect('modelSelect', result.data.models);
                this.updateParamOptions();
            }
        } catch (error) {
            console.error('更新数据分类选项失败:', error);
        }
    }
    
    /**
     * 更新项目名选项
     */
    async updateParamOptions() {
        const deviceMark = document.getElementById('deviceSelect').value;
        const modelMark = document.getElementById('modelSelect').value;
        
        try {
            let url = 'php/get_options.php?';
            if (deviceMark) url += `device_mark=${encodeURIComponent(deviceMark)}&`;
            if (modelMark) url += `model_mark=${encodeURIComponent(modelMark)}&`;
            
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.success && result.data.params) {
                this.populateSelect('paramSelect', result.data.params);
                this.loadChartData();
            }
        } catch (error) {
            console.error('更新项目名选项失败:', error);
        }
    }
    
    /**
     * 加载图表数据
     */
    async loadChartData() {
        const paramName = document.getElementById('paramSelect').value;
        if (!paramName) {
            this.showChartMessage('请选择要监控的项目');
            return;
        }

        // 防止重复请求
        if (this.isLoading) {
            return;
        }

        this.isLoading = true;
        this.showLoading('chartContainer');

        try {
            const params = new URLSearchParams({
                param_name: paramName,
                time_range: document.getElementById('timeRange').value
            });
            
            const deviceMark = document.getElementById('deviceSelect').value;
            const modelMark = document.getElementById('modelSelect').value;
            
            if (deviceMark) params.append('device_mark', deviceMark);
            if (modelMark) params.append('model_mark', modelMark);
            
            const response = await fetch(`php/get_data.php?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderChart(result.data);
                this.updateAlerts(result.data.alerts);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载图表数据失败:', error);
            this.showMessage('加载图表数据失败: ' + error.message, 'error');
            this.showChartMessage('加载数据失败');
        } finally {
            this.hideLoading('chartContainer');
            this.isLoading = false;
        }
    }
    
    /**
     * 渲染图表
     */
    renderChart(data) {
        const chartContainer = document.getElementById('chartContainer');
        
        if (!this.chart) {
            this.chart = echarts.init(chartContainer);
        }
        
        const option = {
            title: {
                text: '设备参数监控图表',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    let result = `时间: ${params[0].axisValue}<br/>`;
                    params.forEach(param => {
                        if (param.value !== null) {
                            result += `${param.seriesName}: ${param.value}<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['正常值', '超标值', '基准线'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: data.chart_data.times,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: '数值'
            },
            series: [
                {
                    name: '正常值',
                    type: 'line',
                    smooth: true,
                    data: data.chart_data.normal_values,
                    itemStyle: {
                        color: '#5470c6'
                    },
                    connectNulls: false
                },
                {
                    name: '超标值',
                    type: 'line',
                    smooth: true,
                    data: data.chart_data.abnormal_values,
                    itemStyle: {
                        color: '#ee6666'
                    },
                    connectNulls: false
                },
                {
                    name: '基准线',
                    type: 'line',
                    data: data.chart_data.spec_line,
                    itemStyle: {
                        color: '#fac858'
                    },
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            ]
        };
        
        this.chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
    }
    
    /**
     * 更新异常报警
     */
    updateAlerts(alerts) {
        const alertsSection = document.getElementById('alertsSection');
        const alertsList = document.getElementById('alertsList');
        
        if (alerts && alerts.length > 0) {
            alertsSection.style.display = 'block';
            alertsList.innerHTML = '';
            
            alerts.forEach(alert => {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = `
                    <div class="alert-time">${alert.time}</div>
                    <div class="alert-message">${alert.message}</div>
                `;
                alertsList.appendChild(alertItem);
            });
            
            // 更新异常项目数
            document.getElementById('abnormalItems').textContent = alerts.length;
        } else {
            alertsSection.style.display = 'none';
            document.getElementById('abnormalItems').textContent = '0';
        }
    }
    
    /**
     * 加载统计信息
     */
    async loadStatistics() {
        try {
            const response = await fetch('php/get_options.php');
            const result = await response.json();
            
            if (result.success && result.data.statistics) {
                this.updateStatistics(result.data.statistics);
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }
    
    /**
     * 更新统计信息
     */
    updateStatistics(stats) {
        document.getElementById('onlineDevices').textContent = stats.online_devices || 0;
        document.getElementById('abnormalItems').textContent = stats.abnormal_count || 0;
        
        if (stats.last_update) {
            const lastUpdate = new Date(stats.last_update);
            document.getElementById('lastUpdate').textContent = lastUpdate.toLocaleTimeString();
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoading(containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading(containerId) {
        const loadingElement = document.querySelector(`#${containerId} .loading`);
        if (loadingElement) {
            loadingElement.remove();
        }
    }
    
    /**
     * 显示图表消息
     */
    showChartMessage(message) {
        const chartContainer = document.getElementById('chartContainer');
        chartContainer.innerHTML = `<div class="loading">${message}</div>`;
    }
    
    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        const messageBox = document.getElementById('messageBox');
        messageBox.className = `message-box ${type}`;
        messageBox.textContent = message;
        messageBox.classList.add('show');
        
        setTimeout(() => {
            messageBox.classList.remove('show');
        }, 3000);
    }
    
    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.stopAutoRefresh();
        this.autoRefreshTimer = setInterval(() => {
            if (this.currentTab === 'monitoring' && !this.isLoading) {
                this.loadChartData();
                this.loadStatistics();
            }
        }, 30000); // 30秒刷新一次

        // 显示自动刷新状态
        this.updateAutoRefreshStatus(true);
    }
    
    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
            this.autoRefreshTimer = null;
        }

        // 更新自动刷新状态
        this.updateAutoRefreshStatus(false);
    }

    /**
     * 更新自动刷新状态显示
     */
    updateAutoRefreshStatus(isActive) {
        const autoRefreshLabel = document.querySelector('.auto-refresh');
        if (autoRefreshLabel) {
            if (isActive) {
                autoRefreshLabel.style.color = '#28a745';
                autoRefreshLabel.title = '自动刷新已启用，每30秒更新一次';
            } else {
                autoRefreshLabel.style.color = '#6c757d';
                autoRefreshLabel.title = '自动刷新已禁用';
            }
        }
    }
    
    /**
     * 导出数据
     */
    async exportData() {
        const paramName = document.getElementById('paramSelect').value;
        if (!paramName) {
            this.showMessage('请先选择要导出的项目', 'warning');
            return;
        }

        try {
            const params = new URLSearchParams({
                param_name: paramName,
                time_range: document.getElementById('timeRange').value,
                export: 'csv'
            });

            const deviceMark = document.getElementById('deviceSelect').value;
            const modelMark = document.getElementById('modelSelect').value;

            if (deviceMark) params.append('device_mark', deviceMark);
            if (modelMark) params.append('model_mark', modelMark);

            // 创建下载链接
            const url = `php/export_data.php?${params}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `equipment_data_${paramName}_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showMessage('数据导出成功', 'success');
        } catch (error) {
            this.showMessage('数据导出失败: ' + error.message, 'error');
        }
    }

    /**
     * 加载基准值列表
     */
    async loadSpecList() {
        this.showLoading('specTableBody');

        try {
            const response = await fetch('php/get_specs.php');
            const result = await response.json();

            if (result.success) {
                this.renderSpecTable(result.data.specs);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载基准值列表失败:', error);
            this.showMessage('加载基准值列表失败: ' + error.message, 'error');
            document.getElementById('specTableBody').innerHTML =
                '<tr><td colspan="3" class="loading">加载失败</td></tr>';
        }
    }

    /**
     * 渲染基准值表格
     */
    renderSpecTable(specs) {
        const tbody = document.getElementById('specTableBody');
        tbody.innerHTML = '';

        if (specs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3" class="loading">暂无数据</td></tr>';
            return;
        }

        specs.forEach(spec => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <strong>${spec.param_name}</strong>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 0.25rem;">
                        记录数: ${spec.record_count} | 异常率: ${spec.abnormal_rate}%
                    </div>
                </td>
                <td>
                    <input type="number"
                           class="spec-input"
                           value="${spec.param_spec}"
                           data-param="${spec.param_name}"
                           data-original="${spec.param_spec}"
                           min="0">
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-success btn-sm save-btn"
                                data-param="${spec.param_name}"
                                style="display: none;">
                            保存
                        </button>
                        <button class="btn btn-secondary btn-sm cancel-btn"
                                data-param="${spec.param_name}"
                                style="display: none;">
                            取消
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        // 绑定输入框事件
        this.bindSpecInputEvents();
    }

    /**
     * 绑定基准值输入框事件
     */
    bindSpecInputEvents() {
        document.querySelectorAll('.spec-input').forEach(input => {
            input.addEventListener('input', (e) => {
                const paramName = e.target.dataset.param;
                const originalValue = e.target.dataset.original;
                const currentValue = e.target.value;

                const saveBtn = document.querySelector(`.save-btn[data-param="${paramName}"]`);
                const cancelBtn = document.querySelector(`.cancel-btn[data-param="${paramName}"]`);

                if (currentValue !== originalValue && currentValue !== '') {
                    saveBtn.style.display = 'inline-block';
                    cancelBtn.style.display = 'inline-block';
                } else {
                    saveBtn.style.display = 'none';
                    cancelBtn.style.display = 'none';
                }
            });
        });

        // 绑定保存按钮事件
        document.querySelectorAll('.save-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const paramName = e.target.dataset.param;
                this.saveSpecValue(paramName);
            });
        });

        // 绑定取消按钮事件
        document.querySelectorAll('.cancel-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const paramName = e.target.dataset.param;
                this.cancelSpecEdit(paramName);
            });
        });
    }

    /**
     * 保存基准值
     */
    async saveSpecValue(paramName) {
        const input = document.querySelector(`.spec-input[data-param="${paramName}"]`);
        const newValue = parseInt(input.value);

        if (isNaN(newValue) || newValue < 0) {
            this.showMessage('请输入有效的基准值', 'warning');
            return;
        }

        const saveBtn = document.querySelector(`.save-btn[data-param="${paramName}"]`);
        const cancelBtn = document.querySelector(`.cancel-btn[data-param="${paramName}"]`);

        // 显示加载状态
        saveBtn.disabled = true;
        saveBtn.textContent = '保存中...';

        try {
            const response = await fetch('php/update_spec.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    param_name: paramName,
                    param_spec: newValue
                })
            });

            const result = await response.json();

            if (result.success) {
                // 更新原始值
                input.dataset.original = newValue.toString();

                // 隐藏按钮
                saveBtn.style.display = 'none';
                cancelBtn.style.display = 'none';

                this.showMessage(result.message, 'success');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('保存基准值失败:', error);
            this.showMessage('保存基准值失败: ' + error.message, 'error');
        } finally {
            saveBtn.disabled = false;
            saveBtn.textContent = '保存';
        }
    }

    /**
     * 取消基准值编辑
     */
    cancelSpecEdit(paramName) {
        const input = document.querySelector(`.spec-input[data-param="${paramName}"]`);
        const saveBtn = document.querySelector(`.save-btn[data-param="${paramName}"]`);
        const cancelBtn = document.querySelector(`.cancel-btn[data-param="${paramName}"]`);

        // 恢复原始值
        input.value = input.dataset.original;

        // 隐藏按钮
        saveBtn.style.display = 'none';
        cancelBtn.style.display = 'none';
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.equipmentMonitor = new EquipmentMonitor();
});
