-- 设备核心参数监控系统 - 数据库初始化脚本
-- 创建数据库和表结构，并插入测试数据

-- 创建数据库
CREATE DATABASE IF NOT EXISTS equipment_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE equipment_management;

-- 创建数据表
CREATE TABLE IF NOT EXISTS eqp_data (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    device_mark VARCHAR(50) NOT NULL COMMENT '设备名',
    model_mark VARCHAR(50) NOT NULL COMMENT '数据分类',
    param_name VARCHAR(50) NOT NULL COMMENT '项目名',
    param_value INT(10) NOT NULL COMMENT '项目值',
    param_spec INT(10) NOT NULL COMMENT '项目基准',
    trigger_time_new DATETIME NOT NULL COMMENT '上传时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_device_mark (device_mark),
    INDEX idx_model_mark (model_mark),
    INDEX idx_param_name (param_name),
    INDEX idx_trigger_time (trigger_time_new),
    INDEX idx_composite (device_mark, model_mark, param_name, trigger_time_new)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备参数数据表';

-- 插入测试数据
-- 设备1: 生产线A
INSERT INTO eqp_data (device_mark, model_mark, param_name, param_value, param_spec, trigger_time_new) VALUES
-- 温度数据 (基准值: 75)
('生产线A', '温度监控', '炉温', 72, 75, DATE_SUB(NOW(), INTERVAL 60 MINUTE)),
('生产线A', '温度监控', '炉温', 74, 75, DATE_SUB(NOW(), INTERVAL 59 MINUTE)),
('生产线A', '温度监控', '炉温', 76, 75, DATE_SUB(NOW(), INTERVAL 58 MINUTE)),
('生产线A', '温度监控', '炉温', 78, 75, DATE_SUB(NOW(), INTERVAL 57 MINUTE)),
('生产线A', '温度监控', '炉温', 73, 75, DATE_SUB(NOW(), INTERVAL 56 MINUTE)),
('生产线A', '温度监控', '炉温', 75, 75, DATE_SUB(NOW(), INTERVAL 55 MINUTE)),
('生产线A', '温度监控', '炉温', 77, 75, DATE_SUB(NOW(), INTERVAL 54 MINUTE)),
('生产线A', '温度监控', '炉温', 79, 75, DATE_SUB(NOW(), INTERVAL 53 MINUTE)),
('生产线A', '温度监控', '炉温', 74, 75, DATE_SUB(NOW(), INTERVAL 52 MINUTE)),
('生产线A', '温度监控', '炉温', 76, 75, DATE_SUB(NOW(), INTERVAL 51 MINUTE)),

-- 压力数据 (基准值: 100)
('生产线A', '压力监控', '主压力', 95, 100, DATE_SUB(NOW(), INTERVAL 60 MINUTE)),
('生产线A', '压力监控', '主压力', 98, 100, DATE_SUB(NOW(), INTERVAL 59 MINUTE)),
('生产线A', '压力监控', '主压力', 102, 100, DATE_SUB(NOW(), INTERVAL 58 MINUTE)),
('生产线A', '压力监控', '主压力', 105, 100, DATE_SUB(NOW(), INTERVAL 57 MINUTE)),
('生产线A', '压力监控', '主压力', 97, 100, DATE_SUB(NOW(), INTERVAL 56 MINUTE)),
('生产线A', '压力监控', '主压力', 100, 100, DATE_SUB(NOW(), INTERVAL 55 MINUTE)),
('生产线A', '压力监控', '主压力', 103, 100, DATE_SUB(NOW(), INTERVAL 54 MINUTE)),
('生产线A', '压力监控', '主压力', 99, 100, DATE_SUB(NOW(), INTERVAL 53 MINUTE)),
('生产线A', '压力监控', '主压力', 101, 100, DATE_SUB(NOW(), INTERVAL 52 MINUTE)),
('生产线A', '压力监控', '主压力', 96, 100, DATE_SUB(NOW(), INTERVAL 51 MINUTE)),

-- 设备2: 生产线B
-- 温度数据 (基准值: 80)
('生产线B', '温度监控', '炉温', 78, 80, DATE_SUB(NOW(), INTERVAL 60 MINUTE)),
('生产线B', '温度监控', '炉温', 82, 80, DATE_SUB(NOW(), INTERVAL 59 MINUTE)),
('生产线B', '温度监控', '炉温', 85, 80, DATE_SUB(NOW(), INTERVAL 58 MINUTE)),
('生产线B', '温度监控', '炉温', 79, 80, DATE_SUB(NOW(), INTERVAL 57 MINUTE)),
('生产线B', '温度监控', '炉温', 81, 80, DATE_SUB(NOW(), INTERVAL 56 MINUTE)),
('生产线B', '温度监控', '炉温', 83, 80, DATE_SUB(NOW(), INTERVAL 55 MINUTE)),
('生产线B', '温度监控', '炉温', 77, 80, DATE_SUB(NOW(), INTERVAL 54 MINUTE)),
('生产线B', '温度监控', '炉温', 80, 80, DATE_SUB(NOW(), INTERVAL 53 MINUTE)),
('生产线B', '温度监控', '炉温', 84, 80, DATE_SUB(NOW(), INTERVAL 52 MINUTE)),
('生产线B', '温度监控', '炉温', 82, 80, DATE_SUB(NOW(), INTERVAL 51 MINUTE)),

-- 振动数据 (基准值: 50)
('生产线B', '振动监控', '主轴振动', 45, 50, DATE_SUB(NOW(), INTERVAL 60 MINUTE)),
('生产线B', '振动监控', '主轴振动', 48, 50, DATE_SUB(NOW(), INTERVAL 59 MINUTE)),
('生产线B', '振动监控', '主轴振动', 52, 50, DATE_SUB(NOW(), INTERVAL 58 MINUTE)),
('生产线B', '振动监控', '主轴振动', 55, 50, DATE_SUB(NOW(), INTERVAL 57 MINUTE)),
('生产线B', '振动监控', '主轴振动', 47, 50, DATE_SUB(NOW(), INTERVAL 56 MINUTE)),
('生产线B', '振动监控', '主轴振动', 50, 50, DATE_SUB(NOW(), INTERVAL 55 MINUTE)),
('生产线B', '振动监控', '主轴振动', 53, 50, DATE_SUB(NOW(), INTERVAL 54 MINUTE)),
('生产线B', '振动监控', '主轴振动', 49, 50, DATE_SUB(NOW(), INTERVAL 53 MINUTE)),
('生产线B', '振动监控', '主轴振动', 51, 50, DATE_SUB(NOW(), INTERVAL 52 MINUTE)),
('生产线B', '振动监控', '主轴振动', 46, 50, DATE_SUB(NOW(), INTERVAL 51 MINUTE)),

-- 设备3: 包装机
-- 速度数据 (基准值: 120)
('包装机', '速度监控', '包装速度', 115, 120, DATE_SUB(NOW(), INTERVAL 60 MINUTE)),
('包装机', '速度监控', '包装速度', 118, 120, DATE_SUB(NOW(), INTERVAL 59 MINUTE)),
('包装机', '速度监控', '包装速度', 122, 120, DATE_SUB(NOW(), INTERVAL 58 MINUTE)),
('包装机', '速度监控', '包装速度', 125, 120, DATE_SUB(NOW(), INTERVAL 57 MINUTE)),
('包装机', '速度监控', '包装速度', 119, 120, DATE_SUB(NOW(), INTERVAL 56 MINUTE)),
('包装机', '速度监控', '包装速度', 120, 120, DATE_SUB(NOW(), INTERVAL 55 MINUTE)),
('包装机', '速度监控', '包装速度', 123, 120, DATE_SUB(NOW(), INTERVAL 54 MINUTE)),
('包装机', '速度监控', '包装速度', 117, 120, DATE_SUB(NOW(), INTERVAL 53 MINUTE)),
('包装机', '速度监控', '包装速度', 121, 120, DATE_SUB(NOW(), INTERVAL 52 MINUTE)),
('包装机', '速度监控', '包装速度', 116, 120, DATE_SUB(NOW(), INTERVAL 51 MINUTE));

-- 添加更多最近的数据点（最近10分钟）
INSERT INTO eqp_data (device_mark, model_mark, param_name, param_value, param_spec, trigger_time_new) VALUES
('生产线A', '温度监控', '炉温', 75, 75, DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
('生产线A', '温度监控', '炉温', 77, 75, DATE_SUB(NOW(), INTERVAL 9 MINUTE)),
('生产线A', '温度监控', '炉温', 79, 75, DATE_SUB(NOW(), INTERVAL 8 MINUTE)),
('生产线A', '温度监控', '炉温', 76, 75, DATE_SUB(NOW(), INTERVAL 7 MINUTE)),
('生产线A', '温度监控', '炉温', 74, 75, DATE_SUB(NOW(), INTERVAL 6 MINUTE)),
('生产线A', '温度监控', '炉温', 78, 75, DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
('生产线A', '温度监控', '炉温', 80, 75, DATE_SUB(NOW(), INTERVAL 4 MINUTE)),
('生产线A', '温度监控', '炉温', 73, 75, DATE_SUB(NOW(), INTERVAL 3 MINUTE)),
('生产线A', '温度监控', '炉温', 75, 75, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
('生产线A', '温度监控', '炉温', 77, 75, DATE_SUB(NOW(), INTERVAL 1 MINUTE));

-- 创建用户和权限（可选）
-- CREATE USER 'eqp_user'@'localhost' IDENTIFIED BY 'eqp_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON equipment_management.* TO 'eqp_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 显示表结构和数据统计
DESCRIBE eqp_data;

SELECT 
    '数据统计' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT device_mark) as total_devices,
    COUNT(DISTINCT param_name) as total_params,
    MIN(trigger_time_new) as earliest_time,
    MAX(trigger_time_new) as latest_time
FROM eqp_data;

SELECT 
    '设备列表' as info,
    device_mark,
    COUNT(*) as record_count
FROM eqp_data 
GROUP BY device_mark;

SELECT 
    '项目列表' as info,
    param_name,
    param_spec as spec_value,
    COUNT(*) as record_count,
    SUM(CASE WHEN param_value > param_spec THEN 1 ELSE 0 END) as abnormal_count
FROM eqp_data 
GROUP BY param_name, param_spec;

-- 初始化完成提示
SELECT '数据库初始化完成！' as message;
