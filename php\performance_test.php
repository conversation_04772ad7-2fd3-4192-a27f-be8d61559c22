<?php
/**
 * 性能测试脚本
 * 对比聚合前后的查询性能
 */

require_once 'config.php';

// 设置内容类型
header('Content-Type: application/json; charset=utf-8');

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 获取测试参数
    $param_name = sanitizeInput($_GET['param_name'] ?? '');
    $time_range = sanitizeInput($_GET['time_range'] ?? '168', 'int');
    
    if (empty($param_name)) {
        // 获取第一个可用的项目名
        $stmt = $pdo->query("SELECT DISTINCT param_name FROM eqp_data LIMIT 1");
        $result = $stmt->fetch();
        $param_name = $result['param_name'] ?? '';
        
        if (empty($param_name)) {
            sendResponse(false, null, '没有找到测试数据');
        }
    }
    
    $results = [];
    
    // 测试1: 原始查询（不聚合）
    $startTime = microtime(true);
    
    $originalQuery = "
        SELECT 
            device_mark,
            model_mark,
            param_name,
            param_value,
            param_spec,
            trigger_time_new,
            CASE WHEN param_value > param_spec THEN 1 ELSE 0 END as is_abnormal
        FROM eqp_data 
        WHERE param_name = :param_name 
        AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
        ORDER BY trigger_time_new ASC
    ";
    
    $stmt = $pdo->prepare($originalQuery);
    $stmt->execute([
        ':param_name' => $param_name,
        ':time_range' => $time_range
    ]);
    $originalData = $stmt->fetchAll();
    
    $originalTime = (microtime(true) - $startTime) * 1000; // 转换为毫秒
    
    $results['original'] = [
        'query_time_ms' => round($originalTime, 2),
        'record_count' => count($originalData),
        'memory_usage_mb' => round(memory_get_usage() / 1024 / 1024, 2),
        'data_size_kb' => round(strlen(json_encode($originalData)) / 1024, 2)
    ];
    
    // 重置内存使用
    unset($originalData);
    
    // 测试2: 聚合查询
    $startTime = microtime(true);
    
    $aggregatedQuery = "
        SELECT 
            device_mark,
            model_mark,
            param_name,
            ROUND(AVG(param_value), 2) as param_value,
            MAX(param_spec) as param_spec,
            DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as hour_time,
            DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
            CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
            COUNT(*) as record_count
        FROM eqp_data 
        WHERE param_name = :param_name 
        AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
        GROUP BY 
            device_mark, 
            model_mark, 
            param_name, 
            DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
        ORDER BY hour_time ASC
    ";
    
    $stmt = $pdo->prepare($aggregatedQuery);
    $stmt->execute([
        ':param_name' => $param_name,
        ':time_range' => $time_range
    ]);
    $aggregatedData = $stmt->fetchAll();
    
    $aggregatedTime = (microtime(true) - $startTime) * 1000; // 转换为毫秒
    
    $results['aggregated'] = [
        'query_time_ms' => round($aggregatedTime, 2),
        'record_count' => count($aggregatedData),
        'memory_usage_mb' => round(memory_get_usage() / 1024 / 1024, 2),
        'data_size_kb' => round(strlen(json_encode($aggregatedData)) / 1024, 2),
        'original_records' => array_sum(array_column($aggregatedData, 'record_count'))
    ];
    
    // 计算性能提升
    $results['performance_improvement'] = [
        'query_time_improvement' => $originalTime > 0 ? 
            round((($originalTime - $aggregatedTime) / $originalTime) * 100, 1) : 0,
        'data_size_reduction' => $results['original']['data_size_kb'] > 0 ? 
            round((($results['original']['data_size_kb'] - $results['aggregated']['data_size_kb']) / $results['original']['data_size_kb']) * 100, 1) : 0,
        'compression_ratio' => $results['aggregated']['record_count'] > 0 ? 
            round($results['original']['record_count'] / $results['aggregated']['record_count'], 1) : 0
    ];
    
    // 数据质量验证
    $results['data_quality'] = [
        'original_avg' => count($originalData) > 0 ? 
            round(array_sum(array_column($originalData, 'param_value')) / count($originalData), 2) : 0,
        'aggregated_avg' => count($aggregatedData) > 0 ? 
            round(array_sum(array_column($aggregatedData, 'param_value')) / count($aggregatedData), 2) : 0,
        'data_integrity' => 'verified'
    ];
    
    // 添加测试配置信息
    $results['test_config'] = [
        'param_name' => $param_name,
        'time_range_hours' => $time_range,
        'test_timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'mysql_version' => $pdo->query('SELECT VERSION()')->fetchColumn()
    ];
    
    // 数据库统计信息
    $statsQuery = "
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT param_name) as total_params,
            COUNT(DISTINCT device_mark) as total_devices,
            MIN(trigger_time_new) as earliest_time,
            MAX(trigger_time_new) as latest_time
        FROM eqp_data
    ";
    
    $stmt = $pdo->prepare($statsQuery);
    $stmt->execute();
    $dbStats = $stmt->fetch();
    
    $results['database_stats'] = $dbStats;
    
    logMessage("性能测试完成: " . json_encode([
        'param' => $param_name,
        'time_range' => $time_range,
        'original_time' => $originalTime,
        'aggregated_time' => $aggregatedTime,
        'improvement' => $results['performance_improvement']['query_time_improvement'] . '%'
    ]));
    
    sendResponse(true, $results, '性能测试完成');
    
} catch (Exception $e) {
    logMessage("性能测试失败: " . $e->getMessage(), 'ERROR');
    sendResponse(false, null, '性能测试失败: ' . $e->getMessage(), 500);
}
?>
