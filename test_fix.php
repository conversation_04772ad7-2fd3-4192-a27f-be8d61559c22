<?php
/**
 * 图表修复验证测试脚本
 */

require_once 'php/config.php';

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

echo "<h1>图表修复验证测试</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .test{margin:10px 0;padding:10px;border:1px solid #ddd;} .success{background:#d4edda;} .error{background:#f8d7da;}</style>";

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<div class='test success'>✓ 数据库连接成功</div>";
    
    // 获取测试参数
    $stmt = $pdo->query("SELECT DISTINCT param_name FROM eqp_data LIMIT 1");
    $result = $stmt->fetch();
    $testParam = $result['param_name'] ?? null;
    
    if (!$testParam) {
        echo "<div class='test error'>✗ 没有找到测试数据</div>";
        exit;
    }
    
    echo "<div class='test success'>✓ 找到测试参数: {$testParam}</div>";
    
    // 测试不同的聚合级别和时间范围组合
    $testCases = [
        ['aggregation' => 'minute', 'time_range' => 24],
        ['aggregation' => 'hour', 'time_range' => 168],
        ['aggregation' => 'hour', 'time_range' => 720],
        ['aggregation' => 'day', 'time_range' => 720],
        ['aggregation' => 'day', 'time_range' => 1440]
    ];
    
    foreach ($testCases as $case) {
        $aggregation = $case['aggregation'];
        $timeRange = $case['time_range'];
        
        echo "<h3>测试: {$aggregation}级聚合, {$timeRange}小时范围</h3>";
        
        try {
            // 模拟API调用
            $_GET = [
                'param_name' => $testParam,
                'time_range' => $timeRange,
                'aggregation_level' => $aggregation
            ];
            
            // 捕获API输出
            ob_start();
            include 'php/get_data.php';
            $output = ob_get_clean();
            
            $result = json_decode($output, true);
            
            if ($result && $result['success']) {
                $dataCount = count($result['data']['chart_data']['times']);
                $totalRecords = $result['data']['statistics']['total_records'] ?? 0;
                
                echo "<div class='test success'>";
                echo "✓ API调用成功<br>";
                echo "- 数据点数: {$dataCount}<br>";
                echo "- 原始记录数: {$totalRecords}<br>";
                echo "- 聚合级别: {$result['data']['statistics']['aggregation_level']}<br>";
                echo "</div>";
            } else {
                $message = $result['message'] ?? '未知错误';
                echo "<div class='test error'>✗ API调用失败: {$message}</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test error'>✗ 测试异常: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<h3>测试总结</h3>";
    echo "<div class='test success'>所有测试完成，请检查上述结果确认修复效果</div>";
    
} catch (Exception $e) {
    echo "<div class='test error'>✗ 测试失败: " . $e->getMessage() . "</div>";
}

echo "<br><a href='debug_chart.html'>使用调试工具进一步测试</a>";
echo "<br><a href='index.html'>返回主系统</a>";
?>
