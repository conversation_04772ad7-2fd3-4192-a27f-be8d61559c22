<?php
/**
 * 获取监控数据API
 * 支持多维度筛选和时间范围查询
 */

require_once 'config.php';

try {
    // 获取请求参数
    $device_mark = sanitizeInput($_GET['device_mark'] ?? '');
    $model_mark = sanitizeInput($_GET['model_mark'] ?? '');
    $param_name = sanitizeInput($_GET['param_name'] ?? '');
    $time_range = sanitizeInput($_GET['time_range'] ?? '24', 'int');
    $start_time = sanitizeInput($_GET['start_time'] ?? '');
    $end_time = sanitizeInput($_GET['end_time'] ?? '');
    
    // 验证必要参数
    if (empty($param_name)) {
        sendResponse(false, null, '请选择要查询的项目名', 400);
    }
    
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 构建查询条件
    $whereConditions = ['param_name = :param_name'];
    $params = [':param_name' => $param_name];
    
    if (!empty($device_mark)) {
        $whereConditions[] = 'device_mark = :device_mark';
        $params[':device_mark'] = $device_mark;
    }
    
    if (!empty($model_mark)) {
        $whereConditions[] = 'model_mark = :model_mark';
        $params[':model_mark'] = $model_mark;
    }
    
    // 时间范围处理
    if (!empty($start_time) && !empty($end_time)) {
        $whereConditions[] = 'trigger_time_new BETWEEN :start_time AND :end_time';
        $params[':start_time'] = $start_time;
        $params[':end_time'] = $end_time;
    } else {
        $whereConditions[] = 'trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)';
        $params[':time_range'] = $time_range;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 查询数据
    $query = "
        SELECT 
            device_mark,
            model_mark,
            param_name,
            param_value,
            param_spec,
            trigger_time_new,
            CASE WHEN param_value > param_spec THEN 1 ELSE 0 END as is_abnormal
        FROM eqp_data 
        WHERE {$whereClause}
        ORDER BY trigger_time_new ASC
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $rawData = $stmt->fetchAll();
    
    if (empty($rawData)) {
        sendResponse(true, [
            'chart_data' => [
                'times' => [],
                'normal_values' => [],
                'abnormal_values' => [],
                'spec_line' => []
            ],
            'statistics' => [
                'total_count' => 0,
                'normal_count' => 0,
                'abnormal_count' => 0,
                'abnormal_rate' => 0,
                'avg_value' => 0,
                'max_value' => 0,
                'min_value' => 0
            ],
            'alerts' => []
        ], '暂无数据');
    }
    
    // 处理图表数据
    $times = [];
    $normalValues = [];
    $abnormalValues = [];
    $specLine = [];
    $alerts = [];
    
    $totalCount = count($rawData);
    $normalCount = 0;
    $abnormalCount = 0;
    $values = [];
    
    foreach ($rawData as $row) {
        $time = date('H:i', strtotime($row['trigger_time_new']));
        $value = (int)$row['param_value'];
        $spec = (int)$row['param_spec'];
        $isAbnormal = (bool)$row['is_abnormal'];
        
        $times[] = $time;
        $specLine[] = $spec;
        $values[] = $value;
        
        if ($isAbnormal) {
            $normalValues[] = null;
            $abnormalValues[] = $value;
            $abnormalCount++;
            
            // 添加异常报警
            $alerts[] = [
                'time' => $row['trigger_time_new'],
                'device' => $row['device_mark'],
                'param' => $row['param_name'],
                'value' => $value,
                'spec' => $spec,
                'message' => "设备 {$row['device_mark']} 的 {$row['param_name']} 超标：{$value} > {$spec}"
            ];
        } else {
            $normalValues[] = $value;
            $abnormalValues[] = null;
            $normalCount++;
        }
    }
    
    // 计算统计信息
    $avgValue = !empty($values) ? round(array_sum($values) / count($values), 2) : 0;
    $maxValue = !empty($values) ? max($values) : 0;
    $minValue = !empty($values) ? min($values) : 0;
    $abnormalRate = $totalCount > 0 ? round(($abnormalCount / $totalCount) * 100, 2) : 0;
    
    // 只保留最近的10条报警
    $alerts = array_slice(array_reverse($alerts), 0, 10);
    
    $responseData = [
        'chart_data' => [
            'times' => $times,
            'normal_values' => $normalValues,
            'abnormal_values' => $abnormalValues,
            'spec_line' => $specLine
        ],
        'statistics' => [
            'total_count' => $totalCount,
            'normal_count' => $normalCount,
            'abnormal_count' => $abnormalCount,
            'abnormal_rate' => $abnormalRate,
            'avg_value' => $avgValue,
            'max_value' => $maxValue,
            'min_value' => $minValue
        ],
        'alerts' => $alerts
    ];
    
    logMessage("获取监控数据成功，参数: " . json_encode($_GET));
    sendResponse(true, $responseData, '获取监控数据成功');
    
} catch (Exception $e) {
    logMessage("获取监控数据失败: " . $e->getMessage(), 'ERROR');
    sendResponse(false, null, '获取监控数据失败: ' . $e->getMessage(), 500);
}
?>
