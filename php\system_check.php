<?php
/**
 * 系统状态检查页面
 * 用于验证系统配置和依赖是否正确
 */

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

// 检查结果数组
$checks = [];

// 1. PHP版本检查
$phpVersion = PHP_VERSION;
$checks['php_version'] = [
    'name' => 'PHP版本',
    'status' => version_compare($phpVersion, '7.4.0', '>='),
    'message' => "当前版本: {$phpVersion} " . (version_compare($phpVersion, '7.4.0', '>=') ? '✓' : '✗ 需要7.4+'),
    'required' => true
];

// 2. PHP扩展检查
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    $checks["ext_{$ext}"] = [
        'name' => "PHP扩展: {$ext}",
        'status' => extension_loaded($ext),
        'message' => extension_loaded($ext) ? '已安装 ✓' : '未安装 ✗',
        'required' => true
    ];
}

// 3. 数据库连接检查
try {
    require_once 'config.php';
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    $checks['database'] = [
        'name' => '数据库连接',
        'status' => true,
        'message' => '连接成功 ✓',
        'required' => true
    ];
    
    // 4. 数据表检查
    $stmt = $pdo->query("SHOW TABLES LIKE 'eqp_data'");
    $tableExists = $stmt->rowCount() > 0;
    
    $checks['table_exists'] = [
        'name' => '数据表 eqp_data',
        'status' => $tableExists,
        'message' => $tableExists ? '表存在 ✓' : '表不存在 ✗',
        'required' => true
    ];
    
    if ($tableExists) {
        // 5. 数据检查
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM eqp_data");
        $result = $stmt->fetch();
        $dataCount = $result['count'];
        
        $checks['data_exists'] = [
            'name' => '测试数据',
            'status' => $dataCount > 0,
            'message' => "数据记录数: {$dataCount} " . ($dataCount > 0 ? '✓' : '⚠ 建议导入测试数据'),
            'required' => false
        ];
    }
    
} catch (Exception $e) {
    $checks['database'] = [
        'name' => '数据库连接',
        'status' => false,
        'message' => '连接失败: ' . $e->getMessage() . ' ✗',
        'required' => true
    ];
}

// 6. 文件权限检查
$writableDirectories = ['logs'];
foreach ($writableDirectories as $dir) {
    $dirPath = __DIR__ . "/../{$dir}";
    $isWritable = is_dir($dirPath) ? is_writable($dirPath) : false;
    
    if (!is_dir($dirPath)) {
        @mkdir($dirPath, 0755, true);
        $isWritable = is_writable($dirPath);
    }
    
    $checks["writable_{$dir}"] = [
        'name' => "目录权限: {$dir}/",
        'status' => $isWritable,
        'message' => $isWritable ? '可写 ✓' : '不可写 ✗',
        'required' => false
    ];
}

// 7. Web服务器检查
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$checks['web_server'] = [
    'name' => 'Web服务器',
    'status' => true,
    'message' => $serverSoftware . ' ✓',
    'required' => false
];

// 8. 内存限制检查
$memoryLimit = ini_get('memory_limit');
$checks['memory_limit'] = [
    'name' => 'PHP内存限制',
    'status' => true,
    'message' => "当前限制: {$memoryLimit} ✓",
    'required' => false
];

// 计算总体状态
$totalChecks = count($checks);
$passedChecks = count(array_filter($checks, function($check) { return $check['status']; }));
$requiredChecks = array_filter($checks, function($check) { return $check['required']; });
$passedRequired = count(array_filter($requiredChecks, function($check) { return $check['status']; }));
$totalRequired = count($requiredChecks);

$overallStatus = $passedRequired === $totalRequired;
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态检查 - 设备核心参数监控系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
        }
        .status-summary {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid #e1e5e9;
        }
        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .checks-list {
            padding: 1.5rem;
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        .check-item.success {
            border-left: 4px solid #28a745;
        }
        .check-item.error {
            border-left: 4px solid #dc3545;
        }
        .check-name {
            font-weight: 500;
        }
        .check-message {
            font-family: monospace;
            font-size: 0.9rem;
        }
        .actions {
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e1e5e9;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            background-color: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #5a6fd8;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>系统状态检查</h1>
            <p>设备核心参数监控系统</p>
        </div>
        
        <div class="status-summary">
            <?php if ($overallStatus): ?>
                <div class="status-badge status-success">
                    ✓ 系统就绪 (<?php echo $passedChecks; ?>/<?php echo $totalChecks; ?>)
                </div>
                <p>所有必需的组件都已正确配置，系统可以正常运行。</p>
            <?php else: ?>
                <div class="status-badge status-error">
                    ✗ 配置不完整 (<?php echo $passedRequired; ?>/<?php echo $totalRequired; ?> 必需项)
                </div>
                <p>请解决以下问题后重新检查。</p>
            <?php endif; ?>
        </div>
        
        <div class="checks-list">
            <?php foreach ($checks as $check): ?>
                <div class="check-item <?php echo $check['status'] ? 'success' : 'error'; ?>">
                    <div class="check-name">
                        <?php echo $check['name']; ?>
                        <?php if ($check['required']): ?>
                            <span style="color: #dc3545;">*</span>
                        <?php endif; ?>
                    </div>
                    <div class="check-message"><?php echo $check['message']; ?></div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="actions">
            <?php if ($overallStatus): ?>
                <a href="../index.html" class="btn">进入监控系统</a>
            <?php endif; ?>
            <a href="?refresh=1" class="btn btn-secondary">重新检查</a>
        </div>
    </div>
</body>
</html>
