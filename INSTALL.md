# 设备核心参数监控系统 - 安装指南

## 环境要求

### 服务器环境
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP版本**: PHP 7.4+ (推荐 PHP 8.0+)
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **PHP扩展**: PDO, PDO_MySQL, JSON, MBString

### 开发环境推荐
- **XAMPP**: 7.4+ (Windows/Mac/Linux)
- **WAMP**: 3.2+ (Windows)
- **MAMP**: 6.0+ (Mac)
- **LAMP**: Ubuntu 20.04+ (Linux)

## 安装步骤

### 1. 下载和部署文件

```bash
# 将项目文件复制到Web服务器目录
# 例如：XAMPP的htdocs目录
cp -r eqp_imp_data /path/to/xampp/htdocs/
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 方法1: 使用phpMyAdmin
-- 1. 打开 http://localhost/phpmyadmin
-- 2. 创建新数据库 "equipment_management"
-- 3. 字符集选择 "utf8mb4_unicode_ci"

-- 方法2: 使用命令行
mysql -u root -p
CREATE DATABASE equipment_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2.2 导入数据库结构和测试数据
```bash
# 使用命令行导入
mysql -u root -p equipment_management < database_init.sql

# 或者使用phpMyAdmin导入database_init.sql文件
```

#### 2.3 配置数据库连接
编辑 `php/config.php` 文件，修改数据库连接信息：

```php
// 数据库配置
define('DB_HOST', 'localhost');     // 数据库主机
define('DB_USER', 'root');          // 数据库用户名
define('DB_PASS', '');              // 数据库密码
define('DB_NAME', 'equipment_management'); // 数据库名
```

### 3. 文件权限设置

```bash
# Linux/Mac 系统
chmod 755 php/
chmod 644 php/*.php
chmod 755 logs/  # 如果logs目录不存在会自动创建

# 确保Web服务器可以写入日志目录
chown -R www-data:www-data logs/  # Apache
# 或
chown -R nginx:nginx logs/        # Nginx
```

### 4. Web服务器配置

#### Apache (.htaccess)
项目根目录创建 `.htaccess` 文件：

```apache
# 启用重写引擎
RewriteEngine On

# 安全设置
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

# PHP设置
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
```

#### Nginx
在Nginx配置中添加：

```nginx
location ~ \.sql$ {
    deny all;
}

location ~ \.md$ {
    deny all;
}

location ~ \.php$ {
    fastcgi_pass 127.0.0.1:9000;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## 访问系统

### 1. 启动服务
```bash
# XAMPP
sudo /opt/lampp/lampp start

# 或者分别启动
sudo systemctl start apache2
sudo systemctl start mysql
```

### 2. 访问地址
打开浏览器访问：
```
http://localhost/eqp_imp_data/
```

### 3. 验证安装
- 检查是否能正常显示页面
- 测试筛选器是否有数据
- 测试图表是否正常显示
- 测试基准管理功能

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
错误: 数据库连接失败
解决: 检查config.php中的数据库配置信息
```

#### 2. 页面显示空白
```
原因: PHP错误或权限问题
解决: 
1. 检查PHP错误日志
2. 确保文件权限正确
3. 检查PHP版本是否符合要求
```

#### 3. 图表不显示
```
原因: ECharts库加载失败
解决: 
1. 检查网络连接
2. 使用本地ECharts文件
3. 检查浏览器控制台错误
```

#### 4. API接口返回错误
```
原因: PHP配置或数据库问题
解决:
1. 检查PHP错误日志
2. 验证数据库表结构
3. 检查API接口权限
```

### 日志文件位置
- **应用日志**: `logs/app.log`
- **PHP错误日志**: `/var/log/apache2/error.log` (Linux)
- **MySQL错误日志**: `/var/log/mysql/error.log` (Linux)

### 调试模式
在 `php/config.php` 中启用调试：

```php
// 开发环境设置
error_reporting(E_ALL);
ini_set('display_errors', 1); // 显示PHP错误
```

## 性能优化

### 1. 数据库优化
```sql
-- 添加索引
ALTER TABLE eqp_data ADD INDEX idx_time_param (trigger_time_new, param_name);
ALTER TABLE eqp_data ADD INDEX idx_device_time (device_mark, trigger_time_new);

-- 定期清理旧数据
DELETE FROM eqp_data WHERE trigger_time_new < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 2. PHP优化
```php
// 启用OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

### 3. 前端优化
- 使用CDN加载ECharts
- 启用Gzip压缩
- 优化图片资源

## 安全建议

1. **修改默认密码**: 更改数据库root密码
2. **限制文件访问**: 配置Web服务器拒绝访问敏感文件
3. **启用HTTPS**: 生产环境使用SSL证书
4. **定期备份**: 设置数据库自动备份
5. **更新依赖**: 定期更新PHP和MySQL版本

## 技术支持

如果遇到问题，请检查：
1. 系统要求是否满足
2. 配置文件是否正确
3. 日志文件中的错误信息
4. 浏览器控制台的错误信息

更多技术支持请参考项目README.md文件。
