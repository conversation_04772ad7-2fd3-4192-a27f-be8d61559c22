<?php
/**
 * 获取筛选选项API
 * 返回设备名、数据分类、项目名列表
 */

require_once 'config.php';

try {
    // 获取筛选参数
    $device_mark = sanitizeInput($_GET['device_mark'] ?? '');
    $model_mark = sanitizeInput($_GET['model_mark'] ?? '');

    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // 构建基础查询条件
    $whereConditions = ["device_mark IS NOT NULL AND device_mark != ''"];
    $params = [];

    // 获取设备名列表
    $deviceQuery = "SELECT DISTINCT device_mark FROM eqp_data WHERE " . implode(' AND ', $whereConditions) . " ORDER BY device_mark";
    $deviceStmt = $pdo->prepare($deviceQuery);
    $deviceStmt->execute($params);
    $devices = $deviceStmt->fetchAll(PDO::FETCH_COLUMN);

    // 获取数据分类列表（根据选择的设备筛选）
    $modelConditions = ["model_mark IS NOT NULL AND model_mark != ''"];
    $modelParams = [];

    if (!empty($device_mark)) {
        $modelConditions[] = 'device_mark = :device_mark';
        $modelParams[':device_mark'] = $device_mark;
    }

    $modelQuery = "SELECT DISTINCT model_mark FROM eqp_data WHERE " . implode(' AND ', $modelConditions) . " ORDER BY model_mark";
    $modelStmt = $pdo->prepare($modelQuery);
    $modelStmt->execute($modelParams);
    $models = $modelStmt->fetchAll(PDO::FETCH_COLUMN);

    // 获取项目名列表（根据选择的设备和分类筛选）
    $paramConditions = ["param_name IS NOT NULL AND param_name != ''"];
    $paramParams = [];

    if (!empty($device_mark)) {
        $paramConditions[] = 'device_mark = :device_mark';
        $paramParams[':device_mark'] = $device_mark;
    }

    if (!empty($model_mark)) {
        $paramConditions[] = 'model_mark = :model_mark';
        $paramParams[':model_mark'] = $model_mark;
    }

    $paramQuery = "SELECT DISTINCT param_name FROM eqp_data WHERE " . implode(' AND ', $paramConditions) . " ORDER BY param_name";
    $paramStmt = $pdo->prepare($paramQuery);
    $paramStmt->execute($paramParams);
    $paramNames = $paramStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // 获取统计信息
    $statsQuery = "
        SELECT 
            COUNT(DISTINCT device_mark) as total_devices,
            COUNT(DISTINCT param_name) as total_params,
            COUNT(*) as total_records,
            SUM(CASE WHEN param_value > param_spec THEN 1 ELSE 0 END) as abnormal_count,
            MAX(trigger_time_new) as last_update
        FROM eqp_data 
        WHERE trigger_time_new >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ";
    $statsStmt = $pdo->prepare($statsQuery);
    $statsStmt->execute();
    $stats = $statsStmt->fetch();
    
    // 获取在线设备数（最近5分钟有数据的设备）
    $onlineQuery = "
        SELECT COUNT(DISTINCT device_mark) as online_devices
        FROM eqp_data 
        WHERE trigger_time_new >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ";
    $onlineStmt = $pdo->prepare($onlineQuery);
    $onlineStmt->execute();
    $onlineResult = $onlineStmt->fetch();
    
    $responseData = [
        'devices' => $devices,
        'models' => $models,
        'params' => $paramNames,
        'statistics' => [
            'online_devices' => (int)$onlineResult['online_devices'],
            'total_devices' => (int)$stats['total_devices'],
            'total_params' => (int)$stats['total_params'],
            'total_records' => (int)$stats['total_records'],
            'abnormal_count' => (int)$stats['abnormal_count'],
            'last_update' => $stats['last_update']
        ]
    ];
    
    logMessage("获取筛选选项成功");
    sendResponse(true, $responseData, '获取筛选选项成功');
    
} catch (Exception $e) {
    logMessage("获取筛选选项失败: " . $e->getMessage(), 'ERROR');
    sendResponse(false, null, '获取筛选选项失败: ' . $e->getMessage(), 500);
}
?>
