<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据聚合测试 - 设备核心参数监控系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .test-section h3 {
            margin-top: 0;
            color: #667eea;
        }
        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        .control-group label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        .control-group select, .control-group input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        .test-button:hover {
            background-color: #5a6fd8;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .result-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .metric-card {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据聚合功能测试</h1>
            <p>验证小时聚合数据的性能和准确性</p>
        </div>
        
        <div class="test-section">
            <h3>测试控制面板</h3>
            <div class="test-controls">
                <div class="control-group">
                    <label for="testParam">测试项目:</label>
                    <select id="testParam">
                        <option value="">请选择项目</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="testTimeRange">时间范围:</label>
                    <select id="testTimeRange">
                        <option value="24">24小时</option>
                        <option value="168">7天</option>
                        <option value="360">15天</option>
                        <option value="720">30天</option>
                        <option value="1440">60天</option>
                    </select>
                </div>
            </div>
            <button class="test-button" onclick="testAggregation()">测试聚合查询</button>
            <button class="test-button" onclick="comparePerformance()">性能对比测试</button>
        </div>
        
        <div class="test-section">
            <h3>聚合数据结果</h3>
            <div id="aggregation-result" class="test-result" style="display: none;"></div>
            
            <div class="performance-metrics" id="performance-metrics" style="display: none;">
                <div class="metric-card">
                    <div class="metric-value" id="data-points">-</div>
                    <div class="metric-label">数据点数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="original-records">-</div>
                    <div class="metric-label">原始记录数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="compression-ratio">-</div>
                    <div class="metric-label">压缩比</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="query-time">-</div>
                    <div class="metric-label">查询时间(ms)</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>数据质量验证</h3>
            <div id="quality-result" class="test-result" style="display: none;"></div>
            
            <table class="comparison-table" id="comparison-table" style="display: none;">
                <thead>
                    <tr>
                        <th>时间段</th>
                        <th>平均值</th>
                        <th>基准值</th>
                        <th>状态</th>
                        <th>原始记录数</th>
                    </tr>
                </thead>
                <tbody id="comparison-tbody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let testStartTime = 0;
        
        // 页面加载时获取项目列表
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const response = await fetch('php/get_options.php');
                const result = await response.json();
                
                if (result.success && result.data.params) {
                    const select = document.getElementById('testParam');
                    result.data.params.forEach(param => {
                        const option = document.createElement('option');
                        option.value = param;
                        option.textContent = param;
                        select.appendChild(option);
                    });
                    
                    // 默认选择第一个项目
                    if (result.data.params.length > 0) {
                        select.value = result.data.params[0];
                    }
                }
            } catch (error) {
                console.error('加载项目列表失败:', error);
            }
        });
        
        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${isSuccess ? 'result-success' : 'result-error'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }
        
        // 测试聚合查询
        async function testAggregation() {
            const paramName = document.getElementById('testParam').value;
            const timeRange = document.getElementById('testTimeRange').value;
            
            if (!paramName) {
                alert('请选择测试项目');
                return;
            }
            
            showResult('aggregation-result', '正在测试聚合查询...', true);
            
            testStartTime = performance.now();
            
            try {
                const response = await fetch(`php/get_data.php?param_name=${encodeURIComponent(paramName)}&time_range=${timeRange}`);
                const result = await response.json();
                
                const queryTime = Math.round(performance.now() - testStartTime);
                
                if (result.success) {
                    showResult('aggregation-result', result, true);
                    
                    // 显示性能指标
                    updatePerformanceMetrics(result.data, queryTime);
                    
                    // 显示数据质量表格
                    updateQualityTable(result.data);
                    
                } else {
                    showResult('aggregation-result', result, false);
                }
            } catch (error) {
                showResult('aggregation-result', {
                    error: error.message
                }, false);
            }
        }
        
        // 更新性能指标
        function updatePerformanceMetrics(data, queryTime) {
            document.getElementById('performance-metrics').style.display = 'grid';
            
            const dataPoints = data.chart_data.times.length;
            const originalRecords = data.statistics.total_records || 0;
            const compressionRatio = originalRecords > 0 ? Math.round((originalRecords / dataPoints) * 10) / 10 : 0;
            
            document.getElementById('data-points').textContent = dataPoints;
            document.getElementById('original-records').textContent = originalRecords;
            document.getElementById('compression-ratio').textContent = compressionRatio + ':1';
            document.getElementById('query-time').textContent = queryTime;
        }
        
        // 更新数据质量表格
        function updateQualityTable(data) {
            const table = document.getElementById('comparison-table');
            const tbody = document.getElementById('comparison-tbody');
            
            table.style.display = 'table';
            tbody.innerHTML = '';
            
            const times = data.chart_data.times;
            const normalValues = data.chart_data.normal_values;
            const abnormalValues = data.chart_data.abnormal_values;
            const specLine = data.chart_data.spec_line;
            
            for (let i = 0; i < times.length; i++) {
                const row = document.createElement('tr');
                const value = normalValues[i] !== null ? normalValues[i] : abnormalValues[i];
                const isAbnormal = abnormalValues[i] !== null;
                
                row.innerHTML = `
                    <td>${times[i]}</td>
                    <td>${value !== null ? value.toFixed(2) : '-'}</td>
                    <td>${specLine[i]}</td>
                    <td style="color: ${isAbnormal ? '#dc3545' : '#28a745'}">
                        ${isAbnormal ? '超标' : '正常'}
                    </td>
                    <td>约60条/小时</td>
                `;
                
                tbody.appendChild(row);
            }
        }
        
        // 性能对比测试
        async function comparePerformance() {
            showResult('quality-result', '正在进行性能对比测试...', true);
            
            const paramName = document.getElementById('testParam').value;
            if (!paramName) {
                alert('请选择测试项目');
                return;
            }
            
            const testResults = [];
            const timeRanges = [24, 168, 360, 720, 1440];
            
            for (const timeRange of timeRanges) {
                const startTime = performance.now();
                
                try {
                    const response = await fetch(`php/get_data.php?param_name=${encodeURIComponent(paramName)}&time_range=${timeRange}`);
                    const result = await response.json();
                    const queryTime = Math.round(performance.now() - startTime);
                    
                    if (result.success) {
                        testResults.push({
                            timeRange: timeRange,
                            dataPoints: result.data.chart_data.times.length,
                            originalRecords: result.data.statistics.total_records || 0,
                            queryTime: queryTime,
                            compressionRatio: result.data.statistics.total_records > 0 ? 
                                Math.round((result.data.statistics.total_records / result.data.chart_data.times.length) * 10) / 10 : 0
                        });
                    }
                } catch (error) {
                    console.error(`测试 ${timeRange} 小时范围失败:`, error);
                }
            }
            
            // 显示对比结果
            let resultText = '性能对比测试结果:\n\n';
            resultText += '时间范围\t数据点数\t原始记录\t压缩比\t查询时间(ms)\n';
            resultText += '─'.repeat(60) + '\n';
            
            testResults.forEach(result => {
                const timeLabel = result.timeRange < 168 ? `${result.timeRange}小时` : 
                                 result.timeRange < 720 ? `${Math.round(result.timeRange/24)}天` : 
                                 `${Math.round(result.timeRange/24)}天`;
                resultText += `${timeLabel}\t\t${result.dataPoints}\t\t${result.originalRecords}\t\t${result.compressionRatio}:1\t\t${result.queryTime}\n`;
            });
            
            resultText += '\n性能分析:\n';
            resultText += `• 数据压缩效果: 平均压缩比 ${Math.round(testResults.reduce((sum, r) => sum + r.compressionRatio, 0) / testResults.length * 10) / 10}:1\n`;
            resultText += `• 查询性能: 平均响应时间 ${Math.round(testResults.reduce((sum, r) => sum + r.queryTime, 0) / testResults.length)}ms\n`;
            resultText += `• 数据传输优化: 减少了 ${Math.round((1 - testResults.reduce((sum, r) => sum + r.dataPoints, 0) / testResults.reduce((sum, r) => sum + r.originalRecords, 0)) * 100)}% 的数据传输量`;
            
            showResult('quality-result', resultText, true);
        }
    </script>
</body>
</html>
