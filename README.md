# 设备核心参数监控Web应用

## 项目概述
一个基于Web的设备核心参数实时监控系统，支持参数基准管理、实时数据可视化和异常报警功能。

## Web应用架构
- **前端框架**：HTML5 + CSS3 + JavaScript (ES6+)
- **图表库**：ECharts 5.x
- **后端语言**：PHP 7.4+
- **数据库**：MySQL 5.7+
- **样式方案**：CSS3 + Flexbox/Grid布局
- **响应式设计**：支持桌面端和移动端
- **API设计**：RESTful API接口

## 功能特性

### 1. 项目基准管理模块
- ✅ 显示所有项目及其对应的基准值列表
- ✅ 支持在线编辑基准值
- ✅ 实时保存和更新功能
- ✅ 操作成功/失败反馈

### 2. 实时监控图表模块
- ✅ 多维度筛选器（设备名、数据分类、项目名、时间范围）
- ✅ ECharts平滑曲线图展示
- ✅ 数据分离显示（正常值/超标值）
- ✅ 实时数据更新

### 3. 扩展监控功能
- ✅ 自动数据刷新（30秒间隔）
- ✅ 异常报警提示
- ✅ 数据统计面板
- ✅ 历史数据导出

## 数据库设计

### 数据表：eqp_data
```sql
CREATE TABLE eqp_data (
    device_mark varchar(50) COMMENT '设备名',
    model_mark varchar(50) COMMENT '数据分类',
    param_name varchar(50) COMMENT '项目名',
    param_value int(10) COMMENT '项目值',
    param_spec int(10) COMMENT '项目基准',
    trigger_time_new datetime COMMENT '上传时间'
);
```

## 项目结构
```
/
├── index.html              # 主页面
├── test.html              # 系统测试页面
├── database_init.sql      # 数据库初始化脚本
├── INSTALL.md             # 详细安装指南
├── css/
│   └── style.css          # 响应式样式文件
├── js/
│   └── main.js            # 主要JavaScript逻辑
├── php/
│   ├── config.php         # 数据库配置和通用函数
│   ├── get_options.php    # 获取筛选选项API
│   ├── get_data.php       # 获取监控数据API
│   ├── get_specs.php      # 获取基准值列表API
│   ├── update_spec.php    # 更新基准值API
│   ├── export_data.php    # 数据导出API
│   └── system_check.php   # 系统状态检查页面
├── logs/                  # 日志文件目录
└── README.md              # 项目说明
```

## 快速开始

### 1. 环境要求
- **PHP**: 7.4+ (推荐 8.0+)
- **MySQL**: 5.7+ 或 MariaDB 10.3+
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+

### 2. 安装步骤

#### 步骤1: 部署文件
```bash
# 将项目文件复制到Web服务器目录
# 例如：XAMPP的htdocs目录
cp -r eqp_imp_data /path/to/xampp/htdocs/
```

#### 步骤2: 数据库初始化
```sql
# 1. 创建数据库
CREATE DATABASE equipment_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 导入数据库结构和测试数据
mysql -u root -p equipment_management < database_init.sql
```

#### 步骤3: 配置数据库连接
编辑 `php/config.php` 文件：
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'equipment_management');
```

#### 步骤4: 验证安装
1. 访问 `http://localhost/eqp_imp_data/php/system_check.php` 检查系统状态
2. 访问 `http://localhost/eqp_imp_data/test.html` 测试API功能
3. 访问 `http://localhost/eqp_imp_data/` 进入监控系统

### 3. 目录权限设置
```bash
# Linux/Mac 系统
chmod 755 php/
chmod 644 php/*.php
chmod 755 logs/
```

详细安装说明请参考 [INSTALL.md](INSTALL.md) 文件。

## 使用指南

### 实时监控模块
1. **选择筛选条件**：
   - 设备名：选择要监控的设备
   - 数据分类：选择数据类型（如温度监控、压力监控等）
   - 项目名：选择具体的监控项目
   - 时间范围：选择查看的时间段

2. **查看图表**：
   - 蓝色线条：正常值（≤基准值）
   - 红色线条：超标值（>基准值）
   - 黄色虚线：基准线

3. **聚合级别控制**：
   - 使用图表下方的滑动条调整数据聚合级别
   - 分钟级：显示原始数据，适合短期详细分析
   - 小时级：显示小时平均值，适合中期趋势分析
   - 天级：显示日平均值，适合长期趋势分析

4. **异常报警**：
   - 当有超标数据时，会在图表下方显示异常报警列表
   - 报警信息包含时间、设备、项目和具体数值
   - 报警内容根据聚合级别自动调整（瞬时值/小时平均值/日平均值）

### 基准管理模块
1. **查看基准值**：显示所有项目的当前基准值和统计信息
2. **编辑基准值**：点击基准值输入框进行修改
3. **保存更改**：修改后点击"保存"按钮确认更改
4. **取消编辑**：点击"取消"按钮恢复原值

### 扩展功能
- **自动刷新**：默认每30秒自动更新数据
- **数据导出**：支持CSV格式导出历史数据
- **响应式设计**：支持桌面端和移动端访问

## API接口文档

### 获取监控数据
- **URL**: `php/get_data.php`
- **方法**: GET
- **参数**: device_mark, model_mark, param_name, start_time, end_time

### 更新基准值
- **URL**: `php/update_spec.php`
- **方法**: POST
- **参数**: param_name, param_spec

### 获取筛选选项
- **URL**: `php/get_options.php`
- **方法**: GET
- **返回**: 设备名、数据分类、项目名列表

## 性能优化

### 数据聚合优化
- **多级聚合**：支持分钟级、小时级、天级三种聚合模式
- **智能切换**：用户可通过滑动条动态切换聚合级别
- **智能压缩**：长时间范围查询时，数据压缩比可达60:1（小时级）或1440:1（天级）
- **SQL优化**：使用GROUP BY和聚合函数在数据库层面处理数据
- **自适应显示**：X轴时间格式根据聚合级别自动调整

### 其他优化措施
- 数据库索引优化
- 图表数据缓存
- 异步API调用
- 响应式图片优化

## 安全特性
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证
- 输入数据验证

## 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发团队
- 前端开发：现代Web技术栈
- 后端开发：PHP + MySQL
- UI/UX设计：扁平化响应式设计

## 版本历史
- v1.0.0 - 初始版本发布
- 功能完整的设备参数监控系统

## 许可证
MIT License
