<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表调试工具 - 设备核心参数监控系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .debug-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #667eea;
        }
        .debug-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        .control-group label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        .control-group select, .control-group input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .debug-button {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        .debug-button:hover {
            background-color: #5a6fd8;
        }
        .debug-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .result-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .status-card {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        .status-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
        }
        .status-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>图表调试工具</h1>
            <p>诊断和修复图表显示问题</p>
        </div>
        
        <div class="debug-section">
            <h3>测试参数配置</h3>
            <div class="debug-controls">
                <div class="control-group">
                    <label for="debugParam">测试项目:</label>
                    <select id="debugParam">
                        <option value="">请选择项目</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="debugTimeRange">时间范围:</label>
                    <select id="debugTimeRange">
                        <option value="24">24小时</option>
                        <option value="168">7天</option>
                        <option value="360">15天</option>
                        <option value="720">30天</option>
                        <option value="1440">60天</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="debugAggregation">聚合级别:</label>
                    <select id="debugAggregation">
                        <option value="minute">分钟级</option>
                        <option value="hour" selected>小时级</option>
                        <option value="day">天级</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="debugDevice">设备名:</label>
                    <select id="debugDevice">
                        <option value="">所有设备</option>
                    </select>
                </div>
            </div>
            <button class="debug-button" onclick="testApiCall()">测试API调用</button>
            <button class="debug-button" onclick="testAllCombinations()">测试所有组合</button>
            <button class="debug-button" onclick="checkSystemStatus()">检查系统状态</button>
        </div>
        
        <div class="debug-section">
            <h3>API测试结果</h3>
            <div id="api-result" class="debug-result" style="display: none;"></div>
            
            <div class="status-grid" id="status-grid" style="display: none;">
                <div class="status-card">
                    <div class="status-value" id="response-time">-</div>
                    <div class="status-label">响应时间(ms)</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="data-points">-</div>
                    <div class="status-label">数据点数</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="original-records">-</div>
                    <div class="status-label">原始记录数</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="compression-ratio">-</div>
                    <div class="status-label">压缩比</div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>系统诊断</h3>
            <div id="system-result" class="debug-result" style="display: none;"></div>
        </div>
        
        <div class="debug-section">
            <h3>问题排查指南</h3>
            <div class="debug-result result-info">
常见问题排查步骤：

1. 检查网络连接
   - 确保能够访问API接口
   - 检查浏览器控制台是否有网络错误

2. 验证参数传递
   - 确认时间范围参数正确传递
   - 验证聚合级别参数有效

3. 检查数据库连接
   - 确认数据库服务正常运行
   - 验证数据表中有测试数据

4. 查看API响应
   - 检查API返回的数据格式
   - 确认数据不为空

5. 验证前端渲染
   - 检查ECharts库是否正确加载
   - 确认图表容器存在

使用本页面的测试工具可以快速定位问题所在。
            </div>
        </div>
    </div>

    <script>
        let testStartTime = 0;
        
        // 页面加载时获取选项
        document.addEventListener('DOMContentLoaded', async () => {
            await loadOptions();
        });
        
        // 加载筛选选项
        async function loadOptions() {
            try {
                const response = await fetch('php/get_options.php');
                const result = await response.json();
                
                if (result.success) {
                    populateSelect('debugParam', result.data.params);
                    populateSelect('debugDevice', result.data.devices);
                }
            } catch (error) {
                console.error('加载选项失败:', error);
            }
        }
        
        // 填充下拉选择框
        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            const firstOption = select.querySelector('option');
            
            select.innerHTML = '';
            select.appendChild(firstOption);
            
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }
        
        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `debug-result ${isSuccess ? 'result-success' : 'result-error'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }
        
        // 测试API调用
        async function testApiCall() {
            const paramName = document.getElementById('debugParam').value;
            const timeRange = document.getElementById('debugTimeRange').value;
            const aggregationLevel = document.getElementById('debugAggregation').value;
            const deviceMark = document.getElementById('debugDevice').value;
            
            if (!paramName) {
                alert('请选择测试项目');
                return;
            }
            
            showResult('api-result', '正在测试API调用...', true);
            
            testStartTime = performance.now();
            
            try {
                const params = new URLSearchParams({
                    param_name: paramName,
                    time_range: timeRange,
                    aggregation_level: aggregationLevel
                });
                
                if (deviceMark) {
                    params.append('device_mark', deviceMark);
                }
                
                const apiUrl = `php/get_data.php?${params}`;
                console.log('测试API URL:', apiUrl);
                
                const response = await fetch(apiUrl);
                const responseTime = Math.round(performance.now() - testStartTime);
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                
                // 显示结果
                showResult('api-result', result, result.success);
                
                // 更新状态卡片
                if (result.success && result.data) {
                    updateStatusCards(result.data, responseTime);
                }
                
            } catch (error) {
                showResult('api-result', {
                    error: error.message,
                    stack: error.stack
                }, false);
            }
        }
        
        // 更新状态卡片
        function updateStatusCards(data, responseTime) {
            document.getElementById('status-grid').style.display = 'grid';
            
            const dataPoints = data.chart_data ? data.chart_data.times.length : 0;
            const originalRecords = data.statistics ? data.statistics.total_records || 0 : 0;
            const compressionRatio = originalRecords > 0 && dataPoints > 0 ? 
                Math.round((originalRecords / dataPoints) * 10) / 10 : 0;
            
            document.getElementById('response-time').textContent = responseTime;
            document.getElementById('data-points').textContent = dataPoints;
            document.getElementById('original-records').textContent = originalRecords;
            document.getElementById('compression-ratio').textContent = compressionRatio + ':1';
        }
        
        // 测试所有组合
        async function testAllCombinations() {
            showResult('api-result', '正在测试所有参数组合...', true);
            
            const paramName = document.getElementById('debugParam').value;
            if (!paramName) {
                alert('请选择测试项目');
                return;
            }
            
            const timeRanges = [24, 168, 360, 720, 1440];
            const aggregationLevels = ['minute', 'hour', 'day'];
            const results = [];
            
            for (const aggregationLevel of aggregationLevels) {
                for (const timeRange of timeRanges) {
                    // 分钟级只测试短时间范围
                    if (aggregationLevel === 'minute' && timeRange > 24) continue;
                    
                    try {
                        const startTime = performance.now();
                        const params = new URLSearchParams({
                            param_name: paramName,
                            time_range: timeRange,
                            aggregation_level: aggregationLevel
                        });
                        
                        const response = await fetch(`php/get_data.php?${params}`);
                        const responseTime = Math.round(performance.now() - startTime);
                        const result = await response.json();
                        
                        results.push({
                            aggregationLevel,
                            timeRange,
                            success: result.success,
                            dataPoints: result.success ? result.data.chart_data.times.length : 0,
                            responseTime,
                            error: result.success ? null : result.message
                        });
                        
                    } catch (error) {
                        results.push({
                            aggregationLevel,
                            timeRange,
                            success: false,
                            error: error.message,
                            responseTime: 0,
                            dataPoints: 0
                        });
                    }
                }
            }
            
            // 显示测试结果
            let resultText = '所有组合测试结果:\n\n';
            resultText += '聚合级别\t时间范围\t成功\t数据点\t响应时间\t错误信息\n';
            resultText += '─'.repeat(80) + '\n';
            
            results.forEach(result => {
                const timeLabel = result.timeRange < 168 ? `${result.timeRange}小时` : 
                                 `${Math.round(result.timeRange/24)}天`;
                const levelLabel = result.aggregationLevel === 'minute' ? '分钟级' :
                                  result.aggregationLevel === 'hour' ? '小时级' : '天级';
                const successLabel = result.success ? '✓' : '✗';
                const errorInfo = result.error ? result.error.substring(0, 30) : '';
                
                resultText += `${levelLabel}\t${timeLabel}\t${successLabel}\t${result.dataPoints}\t${result.responseTime}ms\t${errorInfo}\n`;
            });
            
            const successCount = results.filter(r => r.success).length;
            resultText += `\n测试总结: ${successCount}/${results.length} 个组合成功`;
            
            showResult('api-result', resultText, successCount === results.length);
        }
        
        // 检查系统状态
        async function checkSystemStatus() {
            showResult('system-result', '正在检查系统状态...', true);
            
            const checks = [];
            
            // 检查API可访问性
            try {
                const response = await fetch('php/get_options.php');
                const result = await response.json();
                checks.push({
                    name: 'API可访问性',
                    status: result.success,
                    message: result.success ? '正常' : result.message
                });
            } catch (error) {
                checks.push({
                    name: 'API可访问性',
                    status: false,
                    message: error.message
                });
            }
            
            // 检查数据库连接
            try {
                const response = await fetch('php/system_check.php');
                checks.push({
                    name: '系统检查页面',
                    status: response.ok,
                    message: response.ok ? '可访问' : '无法访问'
                });
            } catch (error) {
                checks.push({
                    name: '系统检查页面',
                    status: false,
                    message: error.message
                });
            }
            
            // 检查ECharts库
            checks.push({
                name: 'ECharts库',
                status: typeof echarts !== 'undefined',
                message: typeof echarts !== 'undefined' ? '已加载' : '未加载'
            });
            
            // 生成检查报告
            let reportText = '系统状态检查报告:\n\n';
            checks.forEach(check => {
                const statusIcon = check.status ? '✓' : '✗';
                reportText += `${statusIcon} ${check.name}: ${check.message}\n`;
            });
            
            const allPassed = checks.every(check => check.status);
            reportText += `\n总体状态: ${allPassed ? '正常' : '存在问题'}`;
            
            showResult('system-result', reportText, allPassed);
        }
    </script>
</body>
</html>
