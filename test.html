<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试 - 设备核心参数监控系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .test-section h3 {
            margin-top: 0;
            color: #667eea;
        }
        .test-button {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        .test-button:hover {
            background-color: #5a6fd8;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .navigation {
            text-align: center;
            margin-bottom: 2rem;
        }
        .nav-link {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
        }
        .nav-link:hover {
            background-color: #5a6268;
        }
        .nav-link.primary {
            background-color: #667eea;
        }
        .nav-link.primary:hover {
            background-color: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>系统功能测试</h1>
            <p>设备核心参数监控系统 - API接口测试</p>
        </div>
        
        <div class="navigation">
            <a href="php/system_check.php" class="nav-link">系统状态检查</a>
            <a href="test_aggregation.html" class="nav-link">数据聚合测试</a>
            <a href="index.html" class="nav-link primary">进入监控系统</a>
        </div>
        
        <div class="test-section">
            <h3>1. 数据库连接测试</h3>
            <button class="test-button" onclick="testDatabaseConnection()">测试数据库连接</button>
            <div id="db-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 筛选选项API测试</h3>
            <button class="test-button" onclick="testGetOptions()">获取筛选选项</button>
            <div id="options-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 基准值管理API测试</h3>
            <button class="test-button" onclick="testGetSpecs()">获取基准值列表</button>
            <button class="test-button" onclick="testUpdateSpec()">测试更新基准值</button>
            <div id="specs-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 监控数据API测试</h3>
            <button class="test-button" onclick="testGetData()">获取监控数据</button>
            <div id="data-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 数据导出API测试</h3>
            <button class="test-button" onclick="testExportData()">测试数据导出</button>
            <div id="export-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>6. 性能测试</h3>
            <button class="test-button" onclick="testPerformance()">测试查询性能</button>
            <div id="performance-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 显示测试结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${isSuccess ? 'result-success' : 'result-error'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }
        
        // 显示信息
        function showInfo(elementId, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'test-result result-info';
            element.textContent = message;
        }
        
        // 测试数据库连接
        async function testDatabaseConnection() {
            showInfo('db-result', '正在测试数据库连接...');
            
            try {
                const response = await fetch('php/get_options.php');
                const result = await response.json();
                
                if (result.success) {
                    showResult('db-result', {
                        status: '数据库连接成功',
                        statistics: result.data.statistics
                    }, true);
                } else {
                    showResult('db-result', {
                        status: '数据库连接失败',
                        error: result.message
                    }, false);
                }
            } catch (error) {
                showResult('db-result', {
                    status: '请求失败',
                    error: error.message
                }, false);
            }
        }
        
        // 测试获取筛选选项
        async function testGetOptions() {
            showInfo('options-result', '正在获取筛选选项...');
            
            try {
                const response = await fetch('php/get_options.php');
                const result = await response.json();
                
                showResult('options-result', result, result.success);
            } catch (error) {
                showResult('options-result', {
                    error: error.message
                }, false);
            }
        }
        
        // 测试获取基准值列表
        async function testGetSpecs() {
            showInfo('specs-result', '正在获取基准值列表...');
            
            try {
                const response = await fetch('php/get_specs.php');
                const result = await response.json();
                
                showResult('specs-result', result, result.success);
            } catch (error) {
                showResult('specs-result', {
                    error: error.message
                }, false);
            }
        }
        
        // 测试更新基准值
        async function testUpdateSpec() {
            showInfo('specs-result', '正在测试更新基准值...');
            
            try {
                // 先获取一个项目名
                const optionsResponse = await fetch('php/get_options.php');
                const optionsResult = await optionsResponse.json();
                
                if (!optionsResult.success || !optionsResult.data.params.length) {
                    showResult('specs-result', {
                        error: '没有找到可测试的项目'
                    }, false);
                    return;
                }
                
                const paramName = optionsResult.data.params[0];
                const testSpec = 999; // 测试值
                
                const response = await fetch('php/update_spec.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        param_name: paramName,
                        param_spec: testSpec
                    })
                });
                
                const result = await response.json();
                showResult('specs-result', result, result.success);
            } catch (error) {
                showResult('specs-result', {
                    error: error.message
                }, false);
            }
        }
        
        // 测试获取监控数据
        async function testGetData() {
            showInfo('data-result', '正在获取监控数据...');
            
            try {
                // 先获取一个项目名
                const optionsResponse = await fetch('php/get_options.php');
                const optionsResult = await optionsResponse.json();
                
                if (!optionsResult.success || !optionsResult.data.params.length) {
                    showResult('data-result', {
                        error: '没有找到可测试的项目'
                    }, false);
                    return;
                }
                
                const paramName = optionsResult.data.params[0];
                const response = await fetch(`php/get_data.php?param_name=${encodeURIComponent(paramName)}&time_range=24`);
                const result = await response.json();
                
                showResult('data-result', result, result.success);
            } catch (error) {
                showResult('data-result', {
                    error: error.message
                }, false);
            }
        }
        
        // 测试数据导出
        async function testExportData() {
            showInfo('export-result', '正在测试数据导出...');
            
            try {
                // 先获取一个项目名
                const optionsResponse = await fetch('php/get_options.php');
                const optionsResult = await optionsResponse.json();
                
                if (!optionsResult.success || !optionsResult.data.params.length) {
                    showResult('export-result', {
                        error: '没有找到可测试的项目'
                    }, false);
                    return;
                }
                
                const paramName = optionsResult.data.params[0];
                const exportUrl = `php/export_data.php?param_name=${encodeURIComponent(paramName)}&time_range=24&export=csv`;
                
                showResult('export-result', {
                    status: '导出URL生成成功',
                    url: exportUrl,
                    note: '点击下面的链接测试下载功能'
                }, true);
                
                // 创建下载链接
                const linkElement = document.createElement('a');
                linkElement.href = exportUrl;
                linkElement.textContent = '点击下载测试文件';
                linkElement.style.display = 'block';
                linkElement.style.marginTop = '10px';
                linkElement.style.color = '#667eea';
                
                const resultElement = document.getElementById('export-result');
                resultElement.appendChild(linkElement);
                
            } catch (error) {
                showResult('export-result', {
                    error: error.message
                }, false);
            }
        }

        // 测试查询性能
        async function testPerformance() {
            showInfo('performance-result', '正在进行性能测试...');

            try {
                // 先获取一个项目名
                const optionsResponse = await fetch('php/get_options.php');
                const optionsResult = await optionsResponse.json();

                if (!optionsResult.success || !optionsResult.data.params.length) {
                    showResult('performance-result', {
                        error: '没有找到可测试的项目'
                    }, false);
                    return;
                }

                const paramName = optionsResult.data.params[0];
                const response = await fetch(`php/performance_test.php?param_name=${encodeURIComponent(paramName)}&time_range=168`);
                const result = await response.json();

                if (result.success) {
                    // 格式化性能测试结果
                    const data = result.data;
                    let formattedResult = `性能测试结果 (${data.test_config.param_name}):\n\n`;

                    formattedResult += `原始查询:\n`;
                    formattedResult += `  - 查询时间: ${data.original.query_time_ms}ms\n`;
                    formattedResult += `  - 记录数: ${data.original.record_count}\n`;
                    formattedResult += `  - 数据大小: ${data.original.data_size_kb}KB\n\n`;

                    formattedResult += `聚合查询:\n`;
                    formattedResult += `  - 查询时间: ${data.aggregated.query_time_ms}ms\n`;
                    formattedResult += `  - 记录数: ${data.aggregated.record_count}\n`;
                    formattedResult += `  - 数据大小: ${data.aggregated.data_size_kb}KB\n`;
                    formattedResult += `  - 原始记录数: ${data.aggregated.original_records}\n\n`;

                    formattedResult += `性能提升:\n`;
                    formattedResult += `  - 查询时间提升: ${data.performance_improvement.query_time_improvement}%\n`;
                    formattedResult += `  - 数据传输减少: ${data.performance_improvement.data_size_reduction}%\n`;
                    formattedResult += `  - 压缩比: ${data.performance_improvement.compression_ratio}:1\n\n`;

                    formattedResult += `数据质量验证:\n`;
                    formattedResult += `  - 原始平均值: ${data.data_quality.original_avg}\n`;
                    formattedResult += `  - 聚合平均值: ${data.data_quality.aggregated_avg}\n`;
                    formattedResult += `  - 数据完整性: ${data.data_quality.data_integrity}\n`;

                    showResult('performance-result', formattedResult, true);
                } else {
                    showResult('performance-result', result, false);
                }
            } catch (error) {
                showResult('performance-result', {
                    error: error.message
                }, false);
            }
        }
    </script>
</body>
</html>
