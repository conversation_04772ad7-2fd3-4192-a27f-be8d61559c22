<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备核心参数监控系统</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="js/echarts.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <h1 class="logo">设备核心参数监控系统</h1>
            <div class="status-bar">
                <span class="status-item">
                    <span class="status-label">在线设备:</span>
                    <span id="onlineDevices" class="status-value">0</span>
                </span>
                <span class="status-item">
                    <span class="status-label">异常项目:</span>
                    <span id="abnormalItems" class="status-value">0</span>
                </span>
                <span class="status-item">
                    <span class="status-label">最后更新:</span>
                    <span id="lastUpdate" class="status-value">--</span>
                </span>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
        <div class="container">
            <!-- 标签页导航 -->
            <div class="tabs">
                <button class="tab-button active" data-tab="monitoring">实时监控</button>
                <button class="tab-button" data-tab="management">基准管理</button>
            </div>

            <!-- 实时监控模块 -->
            <div id="monitoring" class="tab-content active">
                <!-- 筛选器区域 -->
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="deviceSelect">设备名:</label>
                            <select id="deviceSelect" class="filter-select">
                                <option value="">请选择设备</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="modelSelect">数据分类:</label>
                            <select id="modelSelect" class="filter-select">
                                <option value="">请选择分类</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="paramSelect">项目名:</label>
                            <select id="paramSelect" class="filter-select">
                                <option value="">请选择项目</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="timeRange">时间范围:</label>
                            <select id="timeRange" class="filter-select">
                                <option value="24" selected>24小时</option>
                                <option value="168">7天</option>
                                <option value="360">15天</option>
                                <option value="720">30天</option>
                                <option value="1440">60天</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button id="refreshBtn" class="btn btn-primary">刷新数据</button>
                        <button id="exportBtn" class="btn btn-secondary">导出数据</button>
                        <label class="auto-refresh">
                            <input type="checkbox" id="autoRefresh" checked>
                            自动刷新(30秒)
                        </label>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="chart-section">
                    <div id="chartContainer" class="chart-container">
                        <div id="loadingChart" class="loading">正在加载图表数据...</div>
                    </div>
                </div>

                <!-- 异常报警区域 -->
                <div id="alertsSection" class="alerts-section" style="display: none;">
                    <h3>异常报警</h3>
                    <div id="alertsList" class="alerts-list"></div>
                </div>
            </div>

            <!-- 基准管理模块 -->
            <div id="management" class="tab-content">
                <div class="management-section">
                    <div class="section-header">
                        <h2>项目基准值管理</h2>
                        <button id="refreshSpecBtn" class="btn btn-primary">刷新列表</button>
                    </div>
                    <div class="table-container">
                        <table id="specTable" class="spec-table">
                            <thead>
                                <tr>
                                    <th>项目名</th>
                                    <th>当前基准值</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="specTableBody">
                                <tr>
                                    <td colspan="3" class="loading">正在加载数据...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 消息提示框 -->
    <div id="messageBox" class="message-box"></div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">处理中...</div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
