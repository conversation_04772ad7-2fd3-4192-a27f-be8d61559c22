<?php
/**
 * 更新基准值API
 * 支持单个或批量更新项目基准值
 */

require_once 'config.php';

try {
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(false, null, '只允许POST请求', 405);
    }
    
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendResponse(false, null, 'JSON格式错误', 400);
    }
    
    // 验证必要参数
    $param_name = sanitizeInput($data['param_name'] ?? '');
    $param_spec = sanitizeInput($data['param_spec'] ?? '', 'int');
    
    if (empty($param_name)) {
        sendResponse(false, null, '项目名不能为空', 400);
    }
    
    if ($param_spec === false || $param_spec < 0) {
        sendResponse(false, null, '基准值必须为非负整数', 400);
    }
    
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 检查项目是否存在
        $checkQuery = "SELECT COUNT(*) as count FROM eqp_data WHERE param_name = :param_name";
        $checkStmt = $pdo->prepare($checkQuery);
        $checkStmt->execute([':param_name' => $param_name]);
        $result = $checkStmt->fetch();
        
        if ($result['count'] == 0) {
            throw new Exception("项目 '{$param_name}' 不存在");
        }
        
        // 获取更新前的基准值
        $oldSpecQuery = "SELECT DISTINCT param_spec FROM eqp_data WHERE param_name = :param_name LIMIT 1";
        $oldSpecStmt = $pdo->prepare($oldSpecQuery);
        $oldSpecStmt->execute([':param_name' => $param_name]);
        $oldSpec = $oldSpecStmt->fetch();
        
        // 更新基准值
        $updateQuery = "UPDATE eqp_data SET param_spec = :param_spec WHERE param_name = :param_name";
        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->execute([
            ':param_spec' => $param_spec,
            ':param_name' => $param_name
        ]);
        
        $affectedRows = $updateStmt->rowCount();
        
        if ($affectedRows == 0) {
            throw new Exception("更新失败，没有记录被修改");
        }
        
        // 记录操作日志
        $logData = [
            'action' => 'update_spec',
            'param_name' => $param_name,
            'old_spec' => $oldSpec['param_spec'] ?? null,
            'new_spec' => $param_spec,
            'affected_rows' => $affectedRows,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        // 提交事务
        $pdo->commit();
        
        logMessage("基准值更新成功: " . json_encode($logData));
        
        sendResponse(true, [
            'param_name' => $param_name,
            'old_spec' => $oldSpec['param_spec'] ?? null,
            'new_spec' => $param_spec,
            'affected_rows' => $affectedRows
        ], "项目 '{$param_name}' 的基准值已成功更新为 {$param_spec}");
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    logMessage("更新基准值失败: " . $e->getMessage(), 'ERROR');
    sendResponse(false, null, '更新基准值失败: ' . $e->getMessage(), 500);
}
?>
