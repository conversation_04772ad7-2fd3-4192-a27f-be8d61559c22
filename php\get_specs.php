<?php
/**
 * 获取基准值列表API
 * 返回所有项目的基准值信息
 */

require_once 'config.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 获取所有项目的基准值信息
    $query = "
        SELECT 
            param_name,
            param_spec,
            COUNT(*) as record_count,
            MAX(trigger_time_new) as last_update,
            AVG(param_value) as avg_value,
            SUM(CASE WHEN param_value > param_spec THEN 1 ELSE 0 END) as abnormal_count
        FROM eqp_data 
        WHERE param_name IS NOT NULL AND param_name != ''
        GROUP BY param_name, param_spec
        ORDER BY param_name ASC
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $results = $stmt->fetchAll();
    
    $specs = [];
    foreach ($results as $row) {
        $abnormalRate = $row['record_count'] > 0 ? 
            round(($row['abnormal_count'] / $row['record_count']) * 100, 2) : 0;
        
        $specs[] = [
            'param_name' => $row['param_name'],
            'param_spec' => (int)$row['param_spec'],
            'record_count' => (int)$row['record_count'],
            'last_update' => $row['last_update'],
            'avg_value' => round((float)$row['avg_value'], 2),
            'abnormal_count' => (int)$row['abnormal_count'],
            'abnormal_rate' => $abnormalRate
        ];
    }
    
    // 获取总体统计信息
    $statsQuery = "
        SELECT 
            COUNT(DISTINCT param_name) as total_params,
            COUNT(*) as total_records,
            SUM(CASE WHEN param_value > param_spec THEN 1 ELSE 0 END) as total_abnormal
        FROM eqp_data
    ";
    
    $statsStmt = $pdo->prepare($statsQuery);
    $statsStmt->execute();
    $stats = $statsStmt->fetch();
    
    $responseData = [
        'specs' => $specs,
        'statistics' => [
            'total_params' => (int)$stats['total_params'],
            'total_records' => (int)$stats['total_records'],
            'total_abnormal' => (int)$stats['total_abnormal'],
            'overall_abnormal_rate' => $stats['total_records'] > 0 ? 
                round(($stats['total_abnormal'] / $stats['total_records']) * 100, 2) : 0
        ]
    ];
    
    logMessage("获取基准值列表成功");
    sendResponse(true, $responseData, '获取基准值列表成功');
    
} catch (Exception $e) {
    logMessage("获取基准值列表失败: " . $e->getMessage(), 'ERROR');
    sendResponse(false, null, '获取基准值列表失败: ' . $e->getMessage(), 500);
}
?>
