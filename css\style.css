/* 设备核心参数监控系统 - 样式文件 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
    font-size: 14px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.status-bar {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.status-label {
    font-size: 0.8rem;
    opacity: 0.9;
    margin-bottom: 0.2rem;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
}

/* 主要内容区域 */
.main {
    padding: 2rem 0;
}

/* 标签页 */
.tabs {
    display: flex;
    margin-bottom: 2rem;
    border-bottom: 2px solid #e1e5e9;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    cursor: pointer;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #667eea;
    background-color: #f8f9fa;
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
    font-weight: 600;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 筛选器区域 */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #555;
}

.filter-select {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* 自动刷新开关 */
.auto-refresh {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    margin-left: auto;
}

.auto-refresh input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* 图表区域 */
.chart-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.chart-container {
    width: 100%;
    height: 500px;
    position: relative;
}

/* 异常报警区域 */
.alerts-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.alerts-section h3 {
    color: #dc3545;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alerts-list {
    max-height: 300px;
    overflow-y: auto;
}

.alert-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: #fff5f5;
    border-left: 4px solid #dc3545;
    border-radius: 4px;
}

.alert-time {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.alert-message {
    font-weight: 500;
    color: #721c24;
}

/* 基准管理模块 */
.management-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    color: #333;
    font-size: 1.3rem;
}

.table-container {
    overflow-x: auto;
}

.spec-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.spec-table th,
.spec-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.spec-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    position: sticky;
    top: 0;
}

.spec-table tr:hover {
    background-color: #f8f9fa;
}

.spec-input {
    width: 100px;
    padding: 0.5rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
}

.spec-input:focus {
    outline: none;
    border-color: #667eea;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    color: white;
    font-size: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示框 */
.message-box {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.message-box.show {
    transform: translateX(0);
}

.message-box.success {
    background-color: #28a745;
}

.message-box.error {
    background-color: #dc3545;
}

.message-box.warning {
    background-color: #ffc107;
    color: #212529;
}

.message-box.info {
    background-color: #17a2b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .status-bar {
        gap: 1rem;
        justify-content: center;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .auto-refresh {
        margin-left: 0;
        justify-content: center;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .spec-table {
        font-size: 0.8rem;
    }
    
    .spec-table th,
    .spec-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .tabs {
        flex-direction: column;
    }
    
    .tab-button {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e1e5e9;
        border-right: none;
    }
    
    .tab-button.active {
        border-bottom-color: #667eea;
        border-right-color: transparent;
    }
}
