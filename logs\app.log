[2025-07-31 23:03:58] [INFO] 获取筛选选项成功
[2025-07-31 23:03:58] [INFO] 获取筛选选项成功
[2025-07-31 23:04:02] [INFO] 获取基准值列表成功
[2025-07-31 23:07:16] [INFO] 获取筛选选项成功
[2025-07-31 23:07:16] [INFO] 获取筛选选项成功
[2025-07-31 23:07:24] [INFO] 获取筛选选项成功
[2025-07-31 23:07:24] [INFO] 获取筛选选项成功
[2025-07-31 23:07:24] [INFO] 获取筛选选项成功
[2025-07-31 23:07:35] [INFO] 获取基准值列表成功
[2025-07-31 23:07:42] [INFO] 获取基准值列表成功
[2025-07-31 23:07:43] [INFO] 获取基准值列表成功
[2025-07-31 23:09:33] [INFO] 获取筛选选项成功
[2025-07-31 23:09:33] [INFO] 获取筛选选项成功
[2025-07-31 23:09:37] [INFO] 获取筛选选项成功
[2025-07-31 23:09:38] [INFO] 获取筛选选项成功
[2025-07-31 23:09:38] [INFO] 获取筛选选项成功
[2025-07-31 23:09:40] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:09:43] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:09:45] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:09:50] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:09:58] [INFO] 获取基准值列表成功
[2025-07-31 23:10:01] [INFO] 基准值更新成功: {"action":"update_spec","param_name":"Packing_Arm_Z_Axis","old_spec":0,"new_spec":30,"affected_rows":40814,"timestamp":"2025-07-31 23:10:01","ip":"*************"}
[2025-07-31 23:10:04] [INFO] 获取基准值列表成功
[2025-07-31 23:10:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:10:06] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:10:14] [INFO] 获取筛选选项成功
[2025-07-31 23:10:15] [INFO] 获取筛选选项成功
[2025-07-31 23:10:18] [INFO] 获取筛选选项成功
[2025-07-31 23:10:18] [INFO] 获取筛选选项成功
[2025-07-31 23:10:19] [INFO] 获取筛选选项成功
[2025-07-31 23:10:22] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:10:32] [INFO] 获取筛选选项成功
[2025-07-31 23:10:32] [INFO] 获取筛选选项成功
[2025-07-31 23:10:33] [INFO] 获取筛选选项成功
[2025-07-31 23:10:33] [INFO] 获取筛选选项成功
[2025-07-31 23:10:34] [INFO] 获取筛选选项成功
[2025-07-31 23:10:38] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:11:02] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"720","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-07-31 23:11:02] [INFO] 获取筛选选项成功
[2025-07-31 23:11:14] [INFO] 获取基准值列表成功
[2025-07-31 23:11:18] [INFO] 获取基准值列表成功
[2025-07-31 23:11:22] [INFO] 获取基准值列表成功
[2025-07-31 23:13:24] [INFO] 获取筛选选项成功
[2025-07-31 23:13:26] [INFO] 获取筛选选项成功
[2025-07-31 23:13:29] [INFO] 获取基准值列表成功
[2025-07-31 23:13:33] [INFO] 获取筛选选项成功
[2025-07-31 23:13:35] [INFO] 获取筛选选项成功
[2025-07-31 23:14:13] [INFO] 获取筛选选项成功
[2025-07-31 23:14:13] [INFO] 获取筛选选项成功
[2025-07-31 23:14:16] [INFO] 获取筛选选项成功
[2025-07-31 23:14:16] [INFO] 获取筛选选项成功
[2025-07-31 23:14:16] [INFO] 获取筛选选项成功
[2025-07-31 23:14:17] [INFO] 获取筛选选项成功
[2025-07-31 23:14:24] [INFO] 获取筛选选项成功
[2025-07-31 23:14:24] [INFO] 获取筛选选项成功
[2025-07-31 23:14:25] [INFO] 获取筛选选项成功
[2025-07-31 23:14:25] [INFO] 获取筛选选项成功
[2025-07-31 23:14:26] [INFO] 获取基准值列表成功
[2025-07-31 23:14:55] [INFO] 获取筛选选项成功
[2025-07-31 23:14:59] [INFO] 获取基准值列表成功
[2025-07-31 23:15:00] [INFO] 获取基准值列表成功
[2025-07-31 23:15:01] [INFO] 获取基准值列表成功
[2025-08-04 20:57:25] [INFO] 获取筛选选项成功
[2025-08-04 20:57:25] [INFO] 获取筛选选项成功
[2025-08-04 20:57:34] [INFO] 获取筛选选项成功
[2025-08-04 20:57:34] [INFO] 获取筛选选项成功
[2025-08-04 20:57:34] [INFO] 获取筛选选项成功
[2025-08-04 20:57:34] [INFO] 获取筛选选项成功
[2025-08-04 20:57:41] [INFO] 获取筛选选项成功
[2025-08-04 20:57:41] [INFO] 获取筛选选项成功
[2025-08-04 20:57:43] [INFO] 获取筛选选项成功
[2025-08-04 20:58:00] [INFO] 获取基准值列表成功
[2025-08-04 20:58:09] [INFO] 获取筛选选项成功
[2025-08-04 20:58:10] [INFO] 获取筛选选项成功
[2025-08-04 20:58:10] [INFO] 获取筛选选项成功
[2025-08-04 20:58:10] [INFO] 获取筛选选项成功
[2025-08-04 20:58:12] [INFO] 获取筛选选项成功
[2025-08-04 20:58:12] [INFO] 获取筛选选项成功
[2025-08-04 20:58:13] [INFO] 获取筛选选项成功
[2025-08-04 20:58:17] [INFO] 获取筛选选项成功
[2025-08-04 20:58:17] [INFO] 获取筛选选项成功
[2025-08-04 20:58:30] [INFO] 获取筛选选项成功
[2025-08-04 20:58:30] [INFO] 获取筛选选项成功
[2025-08-04 20:58:32] [INFO] 获取筛选选项成功
[2025-08-04 21:01:19] [INFO] 获取筛选选项成功
[2025-08-04 21:01:19] [INFO] 获取筛选选项成功
[2025-08-04 21:01:22] [INFO] 获取筛选选项成功
[2025-08-04 21:01:22] [INFO] 获取筛选选项成功
[2025-08-04 21:01:22] [INFO] 获取筛选选项成功
[2025-08-04 21:03:57] [INFO] 获取筛选选项成功
[2025-08-04 21:03:57] [INFO] 获取筛选选项成功
[2025-08-04 21:04:22] [INFO] 获取筛选选项成功
[2025-08-04 21:04:22] [INFO] 获取筛选选项成功
[2025-08-04 21:04:24] [INFO] 获取筛选选项成功
[2025-08-04 21:04:26] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:04:27] [INFO] 获取筛选选项成功
[2025-08-04 21:04:27] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:04:36] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:04:57] [INFO] 获取筛选选项成功
[2025-08-04 21:04:57] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:05:47] [INFO] 获取筛选选项成功
[2025-08-04 21:05:47] [INFO] 获取筛选选项成功
[2025-08-04 21:06:17] [INFO] 获取筛选选项成功
[2025-08-04 21:06:47] [INFO] 获取筛选选项成功
[2025-08-04 21:06:56] [INFO] 获取筛选选项成功
[2025-08-04 21:06:56] [INFO] 获取筛选选项成功
[2025-08-04 21:06:57] [INFO] 获取筛选选项成功
[2025-08-04 21:06:57] [INFO] 获取筛选选项成功
[2025-08-04 21:07:03] [INFO] 获取筛选选项成功
[2025-08-04 21:07:03] [INFO] 获取筛选选项成功
[2025-08-04 21:07:06] [INFO] 获取筛选选项成功
[2025-08-04 21:07:06] [INFO] 获取筛选选项成功
[2025-08-04 21:07:07] [INFO] 获取筛选选项成功
[2025-08-04 21:07:11] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:07:21] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:07:29] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:07:33] [INFO] 获取筛选选项成功
[2025-08-04 21:07:33] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:08:04] [INFO] 获取筛选选项成功
[2025-08-04 21:08:04] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:24:46] [INFO] 获取筛选选项成功
[2025-08-04 21:24:46] [INFO] 获取筛选选项成功
[2025-08-04 21:24:51] [INFO] 获取筛选选项成功
[2025-08-04 21:24:51] [INFO] 获取筛选选项成功
[2025-08-04 21:24:52] [INFO] 获取筛选选项成功
[2025-08-04 21:24:56] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:25:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:25:16] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:25:16] [INFO] 获取筛选选项成功
[2025-08-04 21:25:43] [INFO] 获取筛选选项成功
[2025-08-04 21:25:46] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:25:46] [INFO] 获取筛选选项成功
[2025-08-04 21:25:46] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440"}
[2025-08-04 21:25:53] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440"}
[2025-08-04 21:26:16] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:26:16] [INFO] 获取筛选选项成功
[2025-08-04 21:26:46] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:26:46] [INFO] 获取筛选选项成功
[2025-08-04 21:27:16] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:27:16] [INFO] 获取筛选选项成功
[2025-08-04 21:27:46] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:27:46] [INFO] 获取筛选选项成功
[2025-08-04 21:28:16] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:28:16] [INFO] 获取筛选选项成功
[2025-08-04 21:29:17] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:29:17] [INFO] 获取筛选选项成功
[2025-08-04 21:30:18] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:30:18] [INFO] 获取筛选选项成功
[2025-08-04 21:31:19] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:31:19] [INFO] 获取筛选选项成功
[2025-08-04 21:32:20] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:32:20] [INFO] 获取筛选选项成功
[2025-08-04 21:33:21] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:33:21] [INFO] 获取筛选选项成功
[2025-08-04 21:34:12] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 21:34:12] [INFO] 获取筛选选项成功
[2025-08-04 22:07:08] [INFO] 获取筛选选项成功
[2025-08-04 22:07:08] [INFO] 获取筛选选项成功
[2025-08-04 22:07:11] [INFO] 获取筛选选项成功
[2025-08-04 22:07:11] [INFO] 获取筛选选项成功
[2025-08-04 22:07:12] [INFO] 获取筛选选项成功
[2025-08-04 22:07:27] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:07:31] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:07:38] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:07:38] [INFO] 获取筛选选项成功
[2025-08-04 22:07:45] [INFO] 获取筛选选项成功
[2025-08-04 22:07:46] [INFO] 获取筛选选项成功
[2025-08-04 22:07:48] [INFO] 获取筛选选项成功
[2025-08-04 22:07:48] [INFO] 获取筛选选项成功
[2025-08-04 22:07:50] [INFO] 获取筛选选项成功
[2025-08-04 22:07:52] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:07:54] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:07:59] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:07:59] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"minute","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:00] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:00] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:04] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"minute","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:06] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:15] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:15] [INFO] 获取筛选选项成功
[2025-08-04 22:08:20] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:43] [INFO] 获取筛选选项成功
[2025-08-04 22:08:43] [INFO] 获取筛选选项成功
[2025-08-04 22:08:45] [INFO] 获取基准值列表成功
[2025-08-04 22:08:51] [INFO] 获取筛选选项成功
[2025-08-04 22:08:51] [INFO] 获取筛选选项成功
[2025-08-04 22:08:52] [INFO] 获取筛选选项成功
[2025-08-04 22:08:56] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:08:58] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:09:01] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:09:03] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:09:03] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"minute","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:09:35] [INFO] 获取筛选选项成功
[2025-08-04 22:09:35] [INFO] 获取筛选选项成功
[2025-08-04 22:09:37] [INFO] 获取筛选选项成功
[2025-08-04 22:09:37] [INFO] 获取筛选选项成功
[2025-08-04 22:09:38] [INFO] 获取筛选选项成功
[2025-08-04 22:09:43] [INFO] 获取筛选选项成功
[2025-08-04 22:09:43] [INFO] 获取筛选选项成功
[2025-08-04 22:09:44] [INFO] 获取筛选选项成功
[2025-08-04 22:09:44] [INFO] 获取筛选选项成功
[2025-08-04 22:09:45] [INFO] 获取筛选选项成功
[2025-08-04 22:10:13] [INFO] 获取筛选选项成功
[2025-08-04 22:10:22] [INFO] 获取筛选选项成功
[2025-08-04 22:10:22] [INFO] 获取筛选选项成功
[2025-08-04 22:10:23] [INFO] 获取筛选选项成功
[2025-08-04 22:10:23] [INFO] 获取筛选选项成功
[2025-08-04 22:10:24] [INFO] 获取筛选选项成功
[2025-08-04 22:10:52] [INFO] 获取筛选选项成功
[2025-08-04 22:10:57] [INFO] 获取筛选选项成功
[2025-08-04 22:10:57] [INFO] 获取筛选选项成功
[2025-08-04 22:10:59] [INFO] 获取筛选选项成功
[2025-08-04 22:10:59] [INFO] 获取筛选选项成功
[2025-08-04 22:10:59] [INFO] 获取筛选选项成功
[2025-08-04 22:11:29] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:11:40] [INFO] 获取筛选选项成功
[2025-08-04 22:11:40] [INFO] 获取筛选选项成功
[2025-08-04 22:11:44] [INFO] 获取筛选选项成功
[2025-08-04 22:11:44] [INFO] 获取筛选选项成功
[2025-08-04 22:11:46] [INFO] 获取筛选选项成功
[2025-08-04 22:11:48] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:11:51] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:11:54] [INFO] 获取筛选选项成功
[2025-08-04 22:11:54] [INFO] 获取筛选选项成功
[2025-08-04 22:11:59] [INFO] 获取筛选选项成功
[2025-08-04 22:11:59] [INFO] 获取筛选选项成功
[2025-08-04 22:11:59] [INFO] 获取筛选选项成功
[2025-08-04 22:12:00] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:12:19] [INFO] 获取筛选选项成功
[2025-08-04 22:12:19] [INFO] 获取筛选选项成功
[2025-08-04 22:12:24] [INFO] 获取筛选选项成功
[2025-08-04 22:12:24] [INFO] 获取筛选选项成功
[2025-08-04 22:12:24] [INFO] 获取筛选选项成功
[2025-08-04 22:12:25] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:12:27] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:12:38] [INFO] 获取筛选选项成功
[2025-08-04 22:12:38] [INFO] 获取筛选选项成功
[2025-08-04 22:13:08] [INFO] 获取筛选选项成功
[2025-08-04 22:13:38] [INFO] 获取筛选选项成功
[2025-08-04 22:14:08] [INFO] 获取筛选选项成功
[2025-08-04 22:14:38] [INFO] 获取筛选选项成功
[2025-08-04 22:15:08] [INFO] 获取筛选选项成功
[2025-08-04 22:15:38] [INFO] 获取筛选选项成功
[2025-08-04 22:16:08] [INFO] 获取筛选选项成功
[2025-08-04 22:17:09] [INFO] 获取筛选选项成功
[2025-08-04 22:18:10] [INFO] 获取筛选选项成功
[2025-08-04 22:19:11] [INFO] 获取筛选选项成功
[2025-08-04 22:20:12] [INFO] 获取筛选选项成功
[2025-08-04 22:21:13] [INFO] 获取筛选选项成功
[2025-08-04 22:21:51] [INFO] 获取筛选选项成功
[2025-08-04 22:22:08] [INFO] 获取筛选选项成功
[2025-08-04 22:22:25] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute","device_mark":null,"model_mark":null}
[2025-08-04 22:22:25] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                param_value,
                param_spec,
                trigger_time_new as time_field,
                DATE_FORMAT(trigger_time_new, '%H:%i') as display_time,
                CASE WHEN param_value > param_spec THEN 1 ELSE 0 END as is_abnormal,
                1 as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            ORDER BY trigger_time_new ASC
            LIMIT 2000
[2025-08-04 22:22:25] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:22:25] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:22:25] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute"}
[2025-08-04 22:22:31] [INFO] 获取筛选选项成功
[2025-08-04 22:22:31] [INFO] 获取筛选选项成功
[2025-08-04 22:22:38] [INFO] 获取筛选选项成功
[2025-08-04 22:22:38] [INFO] 获取筛选选项成功
[2025-08-04 22:22:39] [INFO] 获取筛选选项成功
[2025-08-04 22:22:39] [INFO] 获取筛选选项成功
[2025-08-04 22:22:40] [INFO] 获取筛选选项成功
[2025-08-04 22:22:40] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:40] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:22:40] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:22:40] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:22:40] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:22:42] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:42] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:22:42] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:22:42] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:22:42] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:45] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:45] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:22:45] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:22:45] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:22:45] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:49] [INFO] 获取筛选选项成功
[2025-08-04 22:22:49] [INFO] 获取筛选选项成功
[2025-08-04 22:22:53] [INFO] 获取筛选选项成功
[2025-08-04 22:22:53] [INFO] 获取筛选选项成功
[2025-08-04 22:22:54] [INFO] 获取筛选选项成功
[2025-08-04 22:22:55] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:55] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:22:55] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:22:55] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:22:55] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:22:56] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:56] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:22:56] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":720}
[2025-08-04 22:22:56] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:22:56] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour"}
[2025-08-04 22:22:57] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:57] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:22:57] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:22:57] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:22:57] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:59] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:22:59] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:22:59] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":720}
[2025-08-04 22:22:59] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:22:59] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour"}
[2025-08-04 22:23:00] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:00] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:00] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:23:00] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:23:00] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:23:01] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:01] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:01] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:23:01] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:23:01] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:03] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:03] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:03] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":720}
[2025-08-04 22:23:03] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:23:03] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour"}
[2025-08-04 22:23:04] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:04] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:04] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:23:04] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:23:04] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:23:07] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:07] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:07] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:23:07] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:23:07] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:23:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:23:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:23:09] [INFO] 获取筛选选项成功
[2025-08-04 22:23:09] [INFO] 获取筛选选项成功
[2025-08-04 22:23:12] [INFO] 获取筛选选项成功
[2025-08-04 22:23:12] [INFO] 获取筛选选项成功
[2025-08-04 22:23:12] [INFO] 获取筛选选项成功
[2025-08-04 22:23:13] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:13] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:13] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:23:13] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:23:13] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:23:14] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:14] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:14] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:23:14] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:23:14] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:18] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:18] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:18] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:23:18] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:23:18] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:23:20] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:20] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:23:20] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:23:20] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:23:20] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:23:59] [INFO] 获取筛选选项成功
[2025-08-04 22:24:07] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":null}
[2025-08-04 22:24:07] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:07] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":time_range":24}
[2025-08-04 22:24:07] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:07] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:24:11] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":null}
[2025-08-04 22:24:11] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:11] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":time_range":1440}
[2025-08-04 22:24:11] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:24:11] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                param_value,
                param_spec,
                trigger_time_new as time_field,
                DATE_FORMAT(trigger_time_new, '%H:%i') as display_time,
                CASE WHEN param_value > param_spec THEN 1 ELSE 0 END as is_abnormal,
                1 as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            ORDER BY trigger_time_new ASC
            LIMIT 2000
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":168}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"hour"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":360}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"hour"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":720}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":1440}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:24:16] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"day"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":168}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"day"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":360}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"day"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":720}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:24:16] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"day"}
[2025-08-04 22:24:16] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:24:16] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:24:16] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":1440}
[2025-08-04 22:24:16] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:24:16] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day"}
[2025-08-04 22:24:21] [INFO] 获取筛选选项成功
[2025-08-04 22:25:04] [INFO] 获取筛选选项成功
[2025-08-04 22:25:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute","device_mark":null,"model_mark":null}
[2025-08-04 22:25:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                param_value,
                param_spec,
                trigger_time_new as time_field,
                DATE_FORMAT(trigger_time_new, '%H:%i') as display_time,
                CASE WHEN param_value > param_spec THEN 1 ELSE 0 END as is_abnormal,
                1 as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            ORDER BY trigger_time_new ASC
            LIMIT 2000
[2025-08-04 22:25:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:25:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:25:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute"}
[2025-08-04 22:25:28] [INFO] 获取筛选选项成功
[2025-08-04 22:25:28] [INFO] 获取筛选选项成功
[2025-08-04 22:25:54] [INFO] 获取筛选选项成功
[2025-08-04 22:26:03] [INFO] 获取筛选选项成功
[2025-08-04 22:26:06] [INFO] 获取筛选选项成功
[2025-08-04 22:26:07] [INFO] 获取筛选选项成功
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                param_value,
                param_spec,
                trigger_time_new as time_field,
                DATE_FORMAT(trigger_time_new, '%H:%i') as display_time,
                CASE WHEN param_value > param_spec THEN 1 ELSE 0 END as is_abnormal,
                1 as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            ORDER BY trigger_time_new ASC
            LIMIT 2000
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"minute"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":168}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"hour"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":360}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"hour"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":720}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":1440}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:26:08] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":24}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"day"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":168}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":168,"aggregation_level":"day"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":360}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":360,"aggregation_level":"day"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":720}
[2025-08-04 22:26:08] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:26:08] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"day"}
[2025-08-04 22:26:08] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":null,"model_mark":null}
[2025-08-04 22:26:08] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:26:08] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":time_range":1440}
[2025-08-04 22:26:09] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:26:09] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day"}
[2025-08-04 22:26:09] [INFO] 获取筛选选项成功
[2025-08-04 22:29:17] [INFO] 获取筛选选项成功
[2025-08-04 22:29:18] [INFO] 获取筛选选项成功
[2025-08-04 22:29:18] [INFO] 获取筛选选项成功
[2025-08-04 22:29:19] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:19] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:29:19] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:29:19] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:29:19] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:29:22] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:22] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:29:22] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:29:22] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:29:22] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:26] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:26] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:29:26] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":720}
[2025-08-04 22:29:26] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:29:26] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":720,"aggregation_level":"hour"}
[2025-08-04 22:29:30] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:30] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:29:30] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:29:30] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:29:30] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:37] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:29:37] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:29:37] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:29:37] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:29:37] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:30:50] [INFO] 获取筛选选项成功
[2025-08-04 22:30:50] [INFO] 获取筛选选项成功
[2025-08-04 22:30:52] [INFO] 获取筛选选项成功
[2025-08-04 22:30:52] [INFO] 获取筛选选项成功
[2025-08-04 22:30:53] [INFO] 获取筛选选项成功
[2025-08-04 22:30:53] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:30:53] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:30:53] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:30:53] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:30:53] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:30:55] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:30:55] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:30:55] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:30:55] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:30:55] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:30:57] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:30:57] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:30:57] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:30:57] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:30:57] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:21] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:21] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:31:21] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:31:21] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:31:21] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:21] [INFO] 获取筛选选项成功
[2025-08-04 22:31:35] [INFO] 获取筛选选项成功
[2025-08-04 22:31:35] [INFO] 获取筛选选项成功
[2025-08-04 22:31:36] [INFO] 获取筛选选项成功
[2025-08-04 22:31:37] [INFO] 获取筛选选项成功
[2025-08-04 22:31:37] [INFO] 获取筛选选项成功
[2025-08-04 22:31:38] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:38] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:31:38] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":24}
[2025-08-04 22:31:38] [INFO] SQL查询完成，返回记录数: 0
[2025-08-04 22:31:38] [INFO] 查询结果为空，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":24,"aggregation_level":"hour"}
[2025-08-04 22:31:39] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:39] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')
            ORDER BY time_field ASC
[2025-08-04 22:31:39] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:31:39] [INFO] SQL查询完成，返回记录数: 643
[2025-08-04 22:31:39] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"hour","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:41] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:31:41] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:31:41] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:31:41] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:31:41] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:32:05] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:32:05] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:32:05] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:32:05] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:32:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:32:05] [INFO] 获取筛选选项成功
[2025-08-04 22:32:35] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:32:35] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:32:35] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:32:35] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:32:35] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:32:35] [INFO] 获取筛选选项成功
[2025-08-04 22:33:05] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:33:05] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:33:05] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:33:05] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:33:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:33:05] [INFO] 获取筛选选项成功
[2025-08-04 22:33:35] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:33:35] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:33:35] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:33:35] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:33:35] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:33:35] [INFO] 获取筛选选项成功
[2025-08-04 22:34:05] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:34:05] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:34:05] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:34:05] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:34:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:34:05] [INFO] 获取筛选选项成功
[2025-08-04 22:34:35] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:34:35] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:34:35] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:34:35] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:34:35] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:34:35] [INFO] 获取筛选选项成功
[2025-08-04 22:35:05] [INFO] 获取监控数据请求: {"param_name":"Packing_Arm_Z_Axis","time_range":1440,"aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:35:05] [INFO] 执行SQL查询: SELECT
                device_mark,
                model_mark,
                param_name,
                ROUND(AVG(param_value), 2) as param_value,
                MAX(param_spec) as param_spec,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d 00:00:00') as time_field,
                DATE_FORMAT(trigger_time_new, '%m-%d') as display_time,
                CASE WHEN AVG(param_value) > MAX(param_spec) THEN 1 ELSE 0 END as is_abnormal,
                COUNT(*) as record_count
            FROM eqp_data
            WHERE param_name = :param_name AND device_mark = :device_mark AND model_mark = :model_mark AND trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)
            GROUP BY
                device_mark,
                model_mark,
                param_name,
                DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
            ORDER BY time_field ASC
[2025-08-04 22:35:05] [INFO] 查询参数: {":param_name":"Packing_Arm_Z_Axis",":device_mark":"3SVI12_VI01_VI01_AA",":model_mark":"MACHINE_ENERGY",":time_range":1440}
[2025-08-04 22:35:05] [INFO] SQL查询完成，返回记录数: 28
[2025-08-04 22:35:05] [INFO] 获取监控数据成功，参数: {"param_name":"Packing_Arm_Z_Axis","time_range":"1440","aggregation_level":"day","device_mark":"3SVI12_VI01_VI01_AA","model_mark":"MACHINE_ENERGY"}
[2025-08-04 22:35:05] [INFO] 获取筛选选项成功
