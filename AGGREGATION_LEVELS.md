# 聚合级别控制功能说明

## 功能概述

聚合级别控制功能允许用户根据分析需求动态切换数据的聚合粒度，提供分钟级、小时级、天级三种不同的数据视图，满足从详细分析到长期趋势观察的各种需求。

## 聚合级别详解

### 1. 分钟级（原始数据）
- **数据来源**：直接从数据库获取原始记录
- **时间格式**：HH:mm（如：14:35）
- **适用场景**：短期详细分析、故障诊断、实时监控
- **数据特点**：
  - 显示每分钟的实际测量值
  - 数据点最多，细节最丰富
  - 适合24小时内的数据查看
  - 异常检测基于瞬时值

### 2. 小时级（小时聚合）
- **数据来源**：按小时分组的平均值
- **时间格式**：MM-DD HH:00（如：07-31 14:00）
- **适用场景**：中期趋势分析、日常监控、性能评估
- **数据特点**：
  - 显示每小时内所有数据的平均值
  - 数据压缩比约60:1
  - 适合7天到30天的数据查看
  - 异常检测基于小时平均值

### 3. 天级（日聚合）
- **数据来源**：按天分组的平均值
- **时间格式**：MM-DD（如：07-31）
- **适用场景**：长期趋势分析、月度/季度报告、历史对比
- **数据特点**：
  - 显示每天内所有数据的平均值
  - 数据压缩比约1440:1
  - 适合30天以上的数据查看
  - 异常检测基于日平均值

## 使用方法

### 1. 界面操作
1. 在监控图表下方找到"数据聚合级别"控制区域
2. 使用横向滑动条选择聚合级别：
   - 最左侧：分钟级
   - 中间：小时级（默认）
   - 最右侧：天级
3. 拖动滑动条时，系统会自动重新加载对应级别的数据

### 2. 自动适配
- **图表标题**：自动显示当前聚合级别
- **Y轴标签**：根据聚合级别显示相应的数值类型
- **工具提示**：鼠标悬停时显示对应级别的详细信息
- **异常报警**：报警消息根据聚合级别调整描述

## 性能对比

### 数据压缩效果
| 聚合级别 | 时间范围 | 原始数据点 | 聚合数据点 | 压缩比 | 查询性能提升 |
|----------|----------|------------|------------|--------|--------------|
| 分钟级   | 24小时   | ~1,440     | 1,440      | 1:1    | 基准         |
| 小时级   | 7天      | ~10,080    | 168        | 60:1   | 70%+         |
| 天级     | 30天     | ~43,200    | 30         | 1440:1 | 90%+         |

### 适用场景建议
| 时间范围 | 推荐聚合级别 | 原因 |
|----------|--------------|------|
| ≤ 24小时 | 分钟级 | 保留完整细节，便于故障诊断 |
| 1-7天    | 小时级 | 平衡细节和性能，适合趋势分析 |
| 7-30天   | 小时级 | 中期趋势分析，性能良好 |
| > 30天   | 天级   | 长期趋势，最佳性能 |

## 技术实现

### 后端API支持
```php
// API参数
$aggregation_level = $_GET['aggregation_level'] ?? 'hour';

// 支持的值：'minute', 'hour', 'day'
```

### SQL查询优化
```sql
-- 分钟级查询
SELECT param_value, DATE_FORMAT(trigger_time_new, '%H:%i') as display_time
FROM eqp_data WHERE ...
ORDER BY trigger_time_new ASC LIMIT 2000

-- 小时级查询  
SELECT AVG(param_value) as param_value, 
       DATE_FORMAT(trigger_time_new, '%m-%d %H:00') as display_time
FROM eqp_data WHERE ...
GROUP BY DATE_FORMAT(trigger_time_new, '%Y-%m-%d %H')

-- 天级查询
SELECT AVG(param_value) as param_value,
       DATE_FORMAT(trigger_time_new, '%m-%d') as display_time  
FROM eqp_data WHERE ...
GROUP BY DATE_FORMAT(trigger_time_new, '%Y-%m-%d')
```

### 前端控制逻辑
```javascript
// 聚合级别配置
this.aggregationLevels = {
    0: { value: 'minute', label: '分钟级', info: '显示原始数据' },
    1: { value: 'hour', label: '小时级', info: '显示小时平均值' },
    2: { value: 'day', label: '天级', info: '显示日平均值' }
};

// 滑动条事件处理
handleAggregationChange(sliderValue) {
    const config = this.aggregationLevels[sliderValue];
    this.currentAggregationLevel = config.value;
    this.loadChartData(); // 重新加载数据
}
```

## 数据质量保证

### 聚合算法
- **平均值计算**：使用SQL的AVG()函数确保数值精度
- **时间对齐**：严格按照时间边界进行分组
- **异常检测**：在对应聚合级别上进行异常判断
- **数据完整性**：保留原始记录数信息

### 精度控制
- **分钟级**：保持原始整数值
- **小时级**：保留2位小数
- **天级**：保留2位小数

## 用户体验优化

### 视觉反馈
- **滑动条样式**：现代化的滑动条设计
- **实时标签**：显示当前选择的聚合级别
- **信息提示**：解释当前级别的适用场景
- **加载状态**：切换时显示加载提示

### 响应式设计
- **桌面端**：横向滑动条布局
- **移动端**：垂直布局，触摸友好
- **自适应**：根据屏幕尺寸调整控件大小

## 最佳实践

### 使用建议
1. **故障排查**：使用分钟级查看详细变化
2. **日常监控**：使用小时级观察趋势
3. **报告分析**：使用天级进行长期对比
4. **性能考虑**：长时间范围优先选择高级别聚合

### 注意事项
1. **数据精度**：聚合会丢失部分细节信息
2. **异常检测**：高级别聚合可能错过短时异常
3. **实时性**：聚合数据的更新频率较低
4. **存储需求**：原始数据仍需保留用于详细分析

## 扩展功能

### 已实现
- ✅ 三级聚合支持（分钟/小时/天）
- ✅ 动态切换界面
- ✅ 自适应图表显示
- ✅ 性能优化

### 计划中
- 🔄 自动聚合级别推荐
- 🔄 自定义聚合时间间隔
- 🔄 聚合数据缓存
- 🔄 批量聚合处理

## 故障排除

### 常见问题
1. **切换无响应**：检查网络连接和API状态
2. **数据不匹配**：验证时间范围和聚合级别组合
3. **性能问题**：分钟级查询限制在24小时内
4. **显示异常**：清除浏览器缓存重新加载

### 调试方法
1. 使用浏览器开发者工具查看API请求
2. 检查控制台错误信息
3. 验证数据库中的原始数据
4. 使用测试页面进行功能验证

## 总结

聚合级别控制功能为用户提供了灵活的数据查看方式，既保证了详细分析的需求，又优化了长期趋势查看的性能。通过智能的聚合算法和用户友好的界面设计，用户可以根据具体需求选择最合适的数据粒度，提升了系统的实用性和用户体验。
